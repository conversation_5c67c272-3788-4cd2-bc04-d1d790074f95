/**
 * Health Monitor for Service Worker
 * 监控Service Worker的健康状态，包括数据库连接和保活机制
 */

import { databaseConnectionManager } from './databaseConnection'
import { keepAliveManager } from './keepAlive'

export class HealthMonitor {
  private static instance: HealthMonitor | null = null
  private healthCheckInterval: NodeJS.Timeout | null = null
  private readonly HEALTH_CHECK_INTERVAL = 5 * 60 * 1000 // 5分钟

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): HealthMonitor {
    if (!HealthMonitor.instance) {
      HealthMonitor.instance = new HealthMonitor()
    }
    return HealthMonitor.instance
  }

  /**
   * 执行健康检查
   */
  async performHealthCheck(): Promise<{
    isHealthy: boolean
    databaseHealth: boolean
    keepAliveHealth: boolean
    issues: string[]
  }> {
    const issues: string[] = []
    let isHealthy = true

    try {
      console.log('【EchoSync】Performing health check...')
      
      // 检查数据库连接状态
      const dbHealth = await databaseConnectionManager.getHealthStatus()
      const databaseHealthy = dbHealth.isHealthy
      
      if (databaseHealthy) {
        console.log('【EchoSync】Database health: ✓ Healthy')
      } else {
        console.warn('【EchoSync】Database health: ✗ Unhealthy')
        issues.push('Database connection unhealthy')
        isHealthy = false
      }
      
      // 检查保活管理器状态
      const keepAliveStatus = await keepAliveManager.getStatus()
      const keepAliveHealthy = keepAliveStatus.isInitialized && keepAliveStatus.alarmExists
      
      if (keepAliveHealthy) {
        console.log('【EchoSync】Keep-alive status: ✓ Active')
      } else {
        console.warn('【EchoSync】Keep-alive status: ✗ Inactive')
        issues.push('Keep-alive mechanism inactive')
        isHealthy = false
      }
      
      // 尝试自动修复问题
      if (!databaseHealthy) {
        console.warn('【EchoSync】Attempting to fix database connection...')
        try {
          await databaseConnectionManager.initialize()
          console.log('【EchoSync】Database connection fixed')
        } catch (error) {
          console.error('【EchoSync】Failed to fix database connection:', error)
          issues.push('Failed to fix database connection')
        }
      }
      
      if (!keepAliveHealthy) {
        console.warn('【EchoSync】Attempting to fix keep-alive mechanism...')
        try {
          await keepAliveManager.initialize()
          console.log('【EchoSync】Keep-alive mechanism fixed')
        } catch (error) {
          console.error('【EchoSync】Failed to fix keep-alive mechanism:', error)
          issues.push('Failed to fix keep-alive mechanism')
        }
      }
      
      console.log('【EchoSync】Health check completed')
      
      return {
        isHealthy,
        databaseHealth: databaseHealthy,
        keepAliveHealth: keepAliveHealthy,
        issues
      }
    } catch (error) {
      console.error('【EchoSync】Health check failed:', error)
      issues.push(`Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      
      return {
        isHealthy: false,
        databaseHealth: false,
        keepAliveHealth: false,
        issues
      }
    }
  }

  /**
   * 启动定期健康检查
   */
  startPeriodicHealthCheck(): void {
    if (this.healthCheckInterval) {
      console.log('【EchoSync】Periodic health check already running')
      return
    }

    console.log('【EchoSync】Starting periodic health check...')
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck()
    }, this.HEALTH_CHECK_INTERVAL)
  }

  /**
   * 停止定期健康检查
   */
  stopPeriodicHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
      console.log('【EchoSync】Periodic health check stopped')
    }
  }

  /**
   * 获取系统状态摘要
   */
  async getSystemStatus(): Promise<{
    serviceWorker: {
      isActive: boolean
      uptime: number
    }
    database: {
      isConnected: boolean
      recordCounts?: {
        platformCount: number
        chatHistoryCount: number
      }
    }
    keepAlive: {
      isActive: boolean
      nextAlarmTime?: number
    }
  }> {
    try {
      // Service Worker状态
      const swUptime = Date.now() - (performance.timeOrigin || Date.now())
      
      // 数据库状态
      const dbHealth = await databaseConnectionManager.getHealthStatus()
      
      // 保活状态
      const keepAliveStatus = await keepAliveManager.getStatus()
      
      return {
        serviceWorker: {
          isActive: true,
          uptime: swUptime
        },
        database: {
          isConnected: dbHealth.isHealthy,
          recordCounts: dbHealth.databaseInfo
        },
        keepAlive: {
          isActive: keepAliveStatus.isInitialized,
          nextAlarmTime: keepAliveStatus.nextAlarmTime
        }
      }
    } catch (error) {
      console.error('【EchoSync】Failed to get system status:', error)
      return {
        serviceWorker: {
          isActive: false,
          uptime: 0
        },
        database: {
          isConnected: false
        },
        keepAlive: {
          isActive: false
        }
      }
    }
  }

  /**
   * 执行紧急修复
   */
  async performEmergencyRepair(): Promise<boolean> {
    try {
      console.log('【EchoSync】Performing emergency repair...')
      
      // 重新初始化数据库连接
      await databaseConnectionManager.initialize()
      
      // 重新初始化保活机制
      await keepAliveManager.initialize()
      
      // 验证修复结果
      const healthCheck = await this.performHealthCheck()
      
      if (healthCheck.isHealthy) {
        console.log('【EchoSync】Emergency repair successful')
        return true
      } else {
        console.error('【EchoSync】Emergency repair failed, issues remain:', healthCheck.issues)
        return false
      }
    } catch (error) {
      console.error('【EchoSync】Emergency repair failed:', error)
      return false
    }
  }
}

// 导出单例实例
export const healthMonitor = HealthMonitor.getInstance()
