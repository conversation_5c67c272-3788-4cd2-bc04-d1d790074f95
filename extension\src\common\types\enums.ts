
// 消息类型
export enum MessageType {
    SYNC_PROMPT = 'SYNC_PROMPT',
    GET_HISTORY = 'GET_HISTORY',
    SAVE_CONVERSATION = 'SAVE_CONVERSATION',
    UPDATE_SETTINGS = 'UPDATE_SETTINGS',
    CAPTURE_PROMPT = 'CAPTURE_PROMPT',
    INJECT_PROMPT = 'INJECT_PROMPT',
    EXTRACT_CONVERSATION = 'EXTRACT_CONVERSATION',
    CHECK_PAGE_VALIDITY = 'CHECK_PAGE_VALIDITY',
    GET_PLATFORM_INFO = 'GET_PLATFORM_INFO',
    SHOW_NOTIFICATION = 'SHOW_NOTIFICATION',
  
    // Favicon相关消息
    UPDATE_PLATFORM_FAVICON = 'UPDATE_PLATFORM_FAVICON',
    CHECK_PLATFORM_FAVICON = 'CHECK_PLATFORM_FAVICON',
  
    // 数据库操作消息类型
    DB_CHAT_HISTORY_CREATE = 'DB_CHAT_HISTORY_CREATE',
    DB_CHAT_HISTORY_GET_LIST = 'DB_CHAT_HISTORY_GET_LIST',
    DB_CHAT_HISTORY_GET_UNIQUE = 'DB_CHAT_HISTORY_GET_UNIQUE',
    DB_CHAT_HISTORY_SEARCH = 'DB_CHAT_HISTORY_SEARCH',
    DB_CHAT_HISTORY_UPDATE = 'DB_CHAT_HISTORY_UPDATE',
    DB_CHAT_HISTORY_DELETE = 'DB_CHAT_HISTORY_DELETE',
    DB_CHAT_HISTORY_GET_BY_UID = 'DB_CHAT_HISTORY_GET_BY_UID',
    DB_CHAT_HISTORY_GET_PROMPTS_WITH_PLATFORMS = 'DB_CHAT_HISTORY_GET_PROMPTS_WITH_PLATFORMS',
  
    DB_PLATFORM_GET_BY_NAME = 'DB_PLATFORM_GET_BY_NAME',
    DB_PLATFORM_GET_BY_DOMAIN = 'DB_PLATFORM_GET_BY_DOMAIN',
    DB_PLATFORM_GET_LIST = 'DB_PLATFORM_GET_LIST' // 获取所有平台列表
  }