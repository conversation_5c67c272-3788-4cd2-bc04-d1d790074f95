# 项目规则测试结果

## 问题1: 我在`InputCapture`中想捕捉到页面的输入组件，并监听输入框聚焦事件，该怎么做？

### 答案：
根据 capture.md 规则文档，在 InputCapture 中捕捉页面输入组件并监听聚焦事件的步骤如下：

1. **查找输入框元素**：
   - 使用 `DOMUtils.findElement()` 方法查找元素
   - 通过 `adapter.mergedSelectors` 获取合并后的选择器
   - 选择器优先级：平台特定 > 通用选择器

2. **监听聚焦事件**：
   - 使用原生 `addEventListener` 监听 `focusin` 事件
   - 记录所有事件监听器以便后续清理

3. **派发自定义事件**：
   - 当输入框获得焦点时，派发 `echosync:input-focused` 事件
   - 使用 `document.dispatchEvent(new CustomEvent())` 派发
   - 事件命名使用 'echosync:' 前缀
   - 通过 CustomEvent 的 detail 属性传递数据

4. **实现必须的方法**：
   - `captureElement()`: 查找并返回目标元素
   - `initEventListener()`: 初始化事件监听
   - `destroy()`: 清理资源和事件监听器

## 问题2: FloatingBubbleInject.ts在监听到了输入框聚焦事件，如何把小球移动到输入框左上方？

### 答案：
根据 inject.md 规则文档，FloatingBubbleInject 移动小球到输入框左上方的步骤如下：

1. **监听聚焦事件**：
   - 在 `setupEventListeners()` 方法中监听 `echosync:input-focused` 事件
   - 使用 `document.addEventListener` 监听自定义事件

2. **获取输入框元素**：
   - 从事件的 detail 属性中获取输入框元素信息
   - 或通过选择器重新查找输入框元素

3. **调用移动方法**：
   - 调用 FloatingBubble 组件的 `moveToInputField()` 方法
   - 该方法会自动计算输入框附近的位置（左上方）

4. **位置计算和应用**：
   - FloatingBubble 组件内部使用 `getBoundingClientRect()` 获取输入框位置
   - 通过 `setPosition()` 方法设置新位置
   - 使用 CSS transform 实现位置变化

5. **边界检测**：
   - 调用 `snapToBoundary()` 方法防止超出视口边界

## 问题3: FloatingBubbleInject在打开历史气泡ui前，想查询数据库存储的提示词列表，应该怎么做？

### 答案：
根据 database.md 规则文档，FloatingBubbleInject 查询数据库提示词列表的步骤如下：

1. **使用消息驱动方式**：
   - 不能直接访问数据库，必须通过消息机制
   - 使用 `MessagingService.sendToBackground()` 方法发送消息

2. **发送查询消息**：
   - 指定正确的 MessageType（如 `DB_PROMPT_GET_LIST`）
   - 消息格式使用 ChromeMessage 结构（包含 type, payload, timestamp）
   - 必须处理返回的 ServiceResult 格式

3. **消息处理流程**：
   - Content Script → MessagingService → Background → Service → DAO → Database
   - Background 的 MessageHandler 会路由到对应的 Service
   - Service 层处理业务逻辑并调用 DAO
   - DAO 层执行具体的数据库查询

4. **处理查询结果**：
   - 接收 ServiceResult 格式的返回数据（包含 success, data, error）
   - 进行错误处理，检查 success 字段
   - 使用 data 字段中的提示词列表数据

5. **在历史气泡显示前调用**：
   - 在 `showHistoryBubble()` 方法中，先发送查询消息
   - 等待数据返回后再显示历史气泡UI
   - 将查询到的提示词列表传递给历史气泡组件

## 问题4: 如果在kimi页面，插件捕捉不到输入框，你会怎么办？

### 答案：
根据 capture.md、mediator.md 等规则文档，当在 kimi 页面捕捉不到输入框时，应按以下步骤排查和解决：

1. **检查选择器配置**：
   - 查看是否存在 KimiAdapter 适配器，如果没有则需要创建
   - 检查 KimiAdapter 的 `getSelectors()` 方法是否提供了正确的 inputField 选择器
   - 验证 CommonSelectors 是否包含 kimi 页面的通用选择器

2. **使用调试工具测试**：
   - 使用 `SelectorTester.testSelectors()` 测试选择器有效性
   - 使用浏览器开发者工具手动验证 CSS 选择器
   - 分析 kimi 页面的实际 DOM 结构

3. **分析页面特点**：
   - 检查输入框是否动态生成（需要等待元素出现）
   - 检查是否使用 Shadow DOM 或 iframe
   - 检查输入框的特殊 CSS 类名或属性

4. **解决方案实施**：
   - 创建或更新 KimiAdapter，继承 BaseAIAdapter
   - 在 `getSelectors()` 中提供 kimi 特定选择器
   - 使用 `DOMUtils.waitForElement()` 处理动态元素
   - 在 ContentScriptManager 中注册新适配器

5. **错误处理和降级**：
   - 使用 `console.warn` 记录选择器查找失败
   - 不阻断其他功能执行
   - 提供降级处理方案
   - 遵循选择器优先级：平台特定 > 通用选择器
