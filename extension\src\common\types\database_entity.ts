// 数据库实体类型定义

// 基础实体类型
export interface DatabaseRow {
  id?: number
  created_at?: number
  updated_at?: number
}

// 平台实体
export interface Platform extends DatabaseRow {
  id?: number
  name: string
  url: string
  icon?: string
  icon_base64?: string
  is_delete?: number
}

// 聊天提示词实体
export interface ChatPrompt extends DatabaseRow {
  chat_prompt: string
  chat_uid: string
  create_time: number
  is_synced: number
  is_delete: number
}

// 聊天历史实体
export interface ChatHistory extends DatabaseRow {
  chat_answer?: string
  chat_uid: string
  platform_id: number
  tags?: string
  chat_group_name?: string
  chat_sort?: number
  p_uid?: string
  create_time: number
  is_synced: number
  is_answered: number
  is_delete: number
}

