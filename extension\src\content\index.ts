import { ContentScriptManager } from './ContentScriptManager'
console.log('【EchoSync】EchoSync Content Script loaded on:', window.location.href)
console.log('【EchoSync】Current hostname:', window.location.hostname)
console.log('【EchoSync】User agent:', navigator.userAgent)


// 全局管理器实例
let globalManager: ContentScriptManager | null = null

/**
 * 初始化或重新初始化管理器
 */
function initializeManager(): void {
  // 销毁现有管理器
  if (globalManager) {
    globalManager.destroy()
    globalManager = null
  }

  // 创建新的管理器
  globalManager = new ContentScriptManager()
}

// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeManager)
} else {
  initializeManager()
}

// 处理SPA路由变化
let lastUrl = location.href
const urlObserver = new MutationObserver(() => {
  const url = location.href
  if (url !== lastUrl) {
    lastUrl = url
    console.log('【EchoSync】URL changed, reinitializing...', url)

    // 延迟重新初始化，等待页面渲染完成
    setTimeout(() => {
      initializeManager()
    }, 1000)
  }
})

urlObserver.observe(document, { subtree: true, childList: true })

// 整页刷新或关闭标签页时彻底清理：
window.addEventListener('beforeunload', () => {
  if (globalManager) {
    globalManager.destroy()
    globalManager = null
  }
  urlObserver.disconnect()
})
