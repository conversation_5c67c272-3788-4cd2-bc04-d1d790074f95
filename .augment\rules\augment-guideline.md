---
type: "agent_requested"
description: "Example description"
---
# EchoSync AI Extension - Augment Guidelines

## 技术栈

### Chrome 插件 (extension/)
- **框架**: React 18 + TypeScript 5
- **构建**: Vite 5 + @crxjs/vite-plugin 2
- **样式**: Tailwind CSS 3 + shadcn/ui
- **数据库**: indexedDB+ dexie
- **状态**: Zustand 4
- **路由**: React Router 6
- **测试**: Jest 29 + React Testing Library

## 核心开发规范

### 必须遵守的要求
1. **文件大小限制**: 单个ts文件不能超过300行，超过必须拆分
2. **简洁设计**: 遵循最简洁设计模式，避免过度设计
3. **继承限制**: 继承不超过2级，更多层次使用组合模式
4. **扩展优先**: 子类优先选择对父类的扩充，而不是完全覆盖
5. **数据库访问**: 所有数据库操作必须通过Message发送给background脚本
6. **测试策略**: 暂时不需要单元测试，专注功能实现

### 技术规范
- **TypeScript**: 使用严格模式
- **代码规范**: 遵循 ESLint + Prettier 配置
- **命名规范**: 组件使用 PascalCase，文件使用 kebab-case
- **React**: 优先使用函数组件和 Hooks
- **状态管理**: 使用 Zustand 进行状态管理
- **样式**: 使用 Tailwind CSS 原子化类名
- 所有跨域请求需在 manifest.json 中声明权限
- 使用 @crxjs/vite-plugin 进行热重载开发
- 网站部署前需配置 Supabase 和 Stripe 环境变量

