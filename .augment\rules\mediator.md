---
type: "development_rules"
description: "中介者模式下的选择器管理和组件协调规则"
---

# 中介者模式和选择器管理规则

## 核心设计原则

### BaseAIAdapter 作为唯一中介者
- 所有 Content 子模块必须通过 BaseAIAdapter 进行通信
- 禁止子模块间的直接交互
- BaseAIAdapter 负责协调所有子模块的行为

### 选择器统一管理
- 平台特定选择器由具体 Adapter 提供
- 通用选择器在 CommonSelectors 中定义
- 通过 mergeSelectors 方法统一合并
- 平台选择器优先级高于通用选择器

## BaseAIAdapter 中介者实现

### 1. 基础结构
```typescript
// 位置: extension/src/content/adapters/BaseAIAdapter.ts
export abstract class BaseAIAdapter {
  protected mergedSelectors: PlatformConfigType | null = null
  protected currentPlatform: Platform | null = null
  
  // 子模块实例
  protected inputCapture: InputCapture | null = null
  protected floatingBubbleInject: FloatingBubbleInject | null = null
  protected archiveButtonInject: ArchiveButtonInject | null = null

  constructor() {
    this.mergeSelectors()
    this.initCapture()
    this.initInject()
    this.setupEventListeners()
  }

  /**
   * 抽象方法：获取平台特定选择器
   * 子类必须实现此方法
   */
  abstract getSelectors(): PlatformConfigType

  /**
   * 合并选择器配置
   */
  protected mergeSelectors(): void {
    const platformSelectors = this.getSelectors()
    const commonSelectors = CommonSelectors
    
    this.mergedSelectors = {
      inputField: [
        ...(platformSelectors.inputField || []),
        ...(commonSelectors.inputField || [])
      ],
      sendButton: [
        ...(platformSelectors.sendButton || []),
        ...(commonSelectors.sendButton || [])
      ],
      messageContainer: [
        ...(platformSelectors.messageContainer || []),
        ...(commonSelectors.messageContainer || [])
      ]
    }
  }

  /**
   * 初始化捕捉模块
   */
  protected initCapture(): void {
    this.inputCapture = new InputCapture(this)
  }

  /**
   * 初始化注入模块
   */
  protected initInject(): void {
    this.floatingBubbleInject = new FloatingBubbleInject(this)
    this.archiveButtonInject = new ArchiveButtonInject(this)
  }

  /**
   * 设置事件监听器
   */
  protected setupEventListeners(): void {
    // 监听输入变化事件
    document.addEventListener('echosync:input-changed', (e: CustomEvent) => {
      this.handleInputChanged(e.detail.value)
    })

    // 监听发送事件
    document.addEventListener('echosync:prompt-send', (e: CustomEvent) => {
      this.handlePromptSend(e.detail.prompt)
    })

    // 监听小球点击事件
    document.addEventListener('echosync:floating-bubble-clicked', (e: CustomEvent) => {
      this.handleBubbleClicked(e.detail)
    })
  }

  /**
   * 处理输入变化
   */
  protected handleInputChanged(value: string): void {
    // 协调各子模块响应输入变化
    if (this.floatingBubbleInject) {
      // 更新小球状态
    }
  }

  /**
   * 处理提示词发送
   */
  protected handlePromptSend(prompt: string): void {
    // 协调数据保存等操作
    this.savePromptToDatabase(prompt)
  }

  /**
   * 获取合并后的选择器
   */
  getMergedSelectors(): PlatformConfigType | null {
    return this.mergedSelectors
  }

  /**
   * 设置当前平台信息
   */
  setCurrentPlatform(platform: Platform): void {
    this.currentPlatform = platform
  }

  /**
   * 销毁所有子模块
   */
  destroy(): void {
    this.inputCapture?.destroy()
    this.floatingBubbleInject?.destroy()
    this.archiveButtonInject?.destroy()
  }
}
```

## 具体平台适配器实现

### 1. ChatGPT 适配器示例
```typescript
// 位置: extension/src/content/adapters/chatgpt.ts
import { BaseAIAdapter } from './BaseAIAdapter'
import { PlatformConfigType } from '../types/PlatformConfigType'

export class ChatGPTAdapter extends BaseAIAdapter {
  /**
   * 获取 ChatGPT 特定选择器
   */
  getSelectors(): PlatformConfigType {
    return {
      inputField: [
        '#prompt-textarea',
        'textarea[data-id="root"]',
        'div[contenteditable="true"][data-id="root"]'
      ],
      sendButton: [
        'button[data-testid="send-button"]',
        'button[aria-label="Send prompt"]',
        'button:has(svg[data-icon="arrow-up"])'
      ],
      messageContainer: [
        'div[data-testid="conversation-turn"]',
        '.conversation-content',
        'div[role="presentation"]:has(div[data-message-author-role])'
      ],
      // ChatGPT 特有的选择器
      regenerateButton: [
        'button[aria-label="Regenerate"]',
        'button:has(svg[data-icon="arrow-clockwise"])'
      ],
      stopButton: [
        'button[aria-label="Stop generating"]',
        'button:has(svg[data-icon="square"])'
      ]
    }
  }

  /**
   * ChatGPT 特有的处理逻辑
   */
  protected handlePromptSend(prompt: string): void {
    super.handlePromptSend(prompt)
    
    // ChatGPT 特有的处理
    this.detectConversationId()
    this.monitorResponse()
  }

  /**
   * 检测对话ID
   */
  private detectConversationId(): void {
    const url = window.location.href
    const match = url.match(/\/c\/([a-f0-9-]+)/)
    if (match) {
      const conversationId = match[1]
      // 保存对话ID
    }
  }

  /**
   * 监控响应生成
   */
  private monitorResponse(): void {
    // 监控 ChatGPT 的响应生成过程
    const stopButton = document.querySelector(this.mergedSelectors?.stopButton?.[0] || '')
    if (stopButton) {
      // 响应正在生成
    }
  }
}
```

### 2. Claude 适配器示例
```typescript
// 位置: extension/src/content/adapters/claude.ts
export class ClaudeAdapter extends BaseAIAdapter {
  getSelectors(): PlatformConfigType {
    return {
      inputField: [
        'div[contenteditable="true"][data-testid="chat-input"]',
        'div[contenteditable="true"].ProseMirror',
        'textarea[placeholder*="Talk to Claude"]'
      ],
      sendButton: [
        'button[aria-label="Send Message"]',
        'button:has(svg[data-icon="send"])',
        'button[data-testid="send-button"]'
      ],
      messageContainer: [
        'div[data-testid="message"]',
        'div[data-is-streaming]',
        '.message-content'
      ]
    }
  }

  /**
   * Claude 特有的处理逻辑
   */
  protected handleInputChanged(value: string): void {
    super.handleInputChanged(value)
    
    // Claude 特有的输入处理
    this.updateCharacterCount(value)
  }

  private updateCharacterCount(value: string): void {
    // Claude 有字符数限制，可以显示字符数
    const charCount = value.length
    // 更新UI显示
  }
}
```

## 选择器配置管理

### 1. 通用选择器定义
```typescript
// 位置: extension/src/content/configs/CommonSelectors.ts
export const CommonSelectors: PlatformConfigType = {
  inputField: [
    'textarea[placeholder*="message"]',
    'textarea[placeholder*="Message"]',
    'textarea[placeholder*="Ask"]',
    'textarea[placeholder*="Type"]',
    'div[contenteditable="true"][role="textbox"]',
    'div[contenteditable="true"][aria-label*="message"]',
    'input[type="text"][placeholder*="message"]'
  ],
  sendButton: [
    'button[type="submit"]',
    'button[aria-label*="Send"]',
    'button[aria-label*="submit"]',
    'button:has(svg[data-icon*="send"])',
    'button:has(svg[data-icon*="arrow"])',
    'button.send-button',
    'button[data-testid*="send"]'
  ],
  messageContainer: [
    'div[role="log"]',
    'div[role="main"]',
    'div.messages',
    'div.conversation',
    'div.chat-messages',
    'main[role="main"]'
  ]
}
```

### 2. 选择器优先级规则
```typescript
/**
 * 选择器优先级（从高到低）：
 * 1. 平台特定选择器（具体 Adapter 提供）
 * 2. 通用选择器（CommonSelectors 提供）
 * 
 * 合并策略：
 * - 平台特定选择器在前，优先匹配
 * - 通用选择器在后，作为降级方案
 * - 按数组顺序依次尝试匹配
 */
protected mergeSelectors(): void {
  const platformSelectors = this.getSelectors()
  const commonSelectors = CommonSelectors
  
  // 合并时平台选择器优先
  this.mergedSelectors = {
    inputField: [
      ...(platformSelectors.inputField || []),
      ...(commonSelectors.inputField || [])
    ],
    // ... 其他选择器类型
  }
}
```

## 组件间通信规则

### 1. 事件驱动通信
```typescript
// 子模块通过自定义事件与中介者通信
export class InputCapture {
  private notifyInputChanged(value: string): void {
    document.dispatchEvent(new CustomEvent('echosync:input-changed', {
      detail: { value }
    }))
  }

  private notifyPromptSend(prompt: string): void {
    document.dispatchEvent(new CustomEvent('echosync:prompt-send', {
      detail: { prompt }
    }))
  }
}

// 中介者监听事件并协调响应
export class BaseAIAdapter {
  protected setupEventListeners(): void {
    document.addEventListener('echosync:input-changed', (e: CustomEvent) => {
      this.handleInputChanged(e.detail.value)
    })
  }
}
```

### 2. 禁止的通信方式
```typescript
// ❌ 错误：子模块间直接通信
export class InputCapture {
  constructor(private floatingBubble: FloatingBubbleInject) {} // 禁止
}

// ❌ 错误：直接调用其他模块方法
export class FloatingBubbleInject {
  private updateInput(): void {
    this.inputCapture.setValue('new value') // 禁止
  }
}

// ✅ 正确：通过中介者通信
export class FloatingBubbleInject {
  private updateInput(): void {
    document.dispatchEvent(new CustomEvent('echosync:update-input', {
      detail: { value: 'new value' }
    }))
  }
}
```

## 新增平台适配器规则

### 1. 创建新适配器
```typescript
// 位置: extension/src/content/adapters/newPlatform.ts
import { BaseAIAdapter } from './BaseAIAdapter'
import { PlatformConfigType } from '../types/PlatformConfigType'

export class NewPlatformAdapter extends BaseAIAdapter {
  /**
   * 必须实现：提供平台特定选择器
   */
  getSelectors(): PlatformConfigType {
    return {
      inputField: [
        // 平台特定的输入框选择器
        '.platform-specific-input',
        'textarea[data-platform="new"]'
      ],
      sendButton: [
        // 平台特定的发送按钮选择器
        '.platform-send-btn',
        'button[data-action="send"]'
      ],
      messageContainer: [
        // 平台特定的消息容器选择器
        '.platform-messages',
        'div[data-role="conversation"]'
      ]
    }
  }

  /**
   * 可选：重写特定的处理逻辑
   */
  protected handlePromptSend(prompt: string): void {
    super.handlePromptSend(prompt)
    
    // 平台特有的处理逻辑
    this.platformSpecificLogic()
  }

  private platformSpecificLogic(): void {
    // 实现平台特有的逻辑
  }
}
```

### 2. 注册新适配器
```typescript
// 位置: extension/src/content/ContentScriptManager.ts
private createAdapter(platformId: string): BaseAIAdapter | null {
  switch (platformId) {
    case 'chatgpt':
      return new ChatGPTAdapter()
    case 'claude':
      return new ClaudeAdapter()
    case 'newPlatform': // 添加新平台
      return new NewPlatformAdapter()
    default:
      return null
  }
}
```

## 选择器调试和优化

### 1. 选择器测试
```typescript
// 开发时的选择器测试工具
export class SelectorTester {
  static testSelectors(selectors: string[]): void {
    console.group('【Selector Test】')
    
    selectors.forEach((selector, index) => {
      try {
        const elements = document.querySelectorAll(selector)
        console.log(`${index + 1}. ${selector}:`, elements.length, 'elements found')
        
        if (elements.length > 0) {
          console.log('   First element:', elements[0])
        }
      } catch (error) {
        console.error(`   Invalid selector: ${selector}`, error)
      }
    })
    
    console.groupEnd()
  }
}

// 使用示例
SelectorTester.testSelectors(this.mergedSelectors?.inputField || [])
```

### 2. 选择器性能监控
```typescript
// 监控选择器查找性能
export class SelectorPerformance {
  static measureFind(selectors: string[]): HTMLElement | null {
    const startTime = performance.now()
    
    for (const selector of selectors) {
      try {
        const element = document.querySelector(selector) as HTMLElement
        if (element) {
          const endTime = performance.now()
          console.log(`【Selector Performance】Found in ${endTime - startTime}ms:`, selector)
          return element
        }
      } catch (error) {
        console.warn('【Selector Performance】Invalid selector:', selector)
      }
    }
    
    const endTime = performance.now()
    console.warn(`【Selector Performance】Not found after ${endTime - startTime}ms`)
    return null
  }
}
```

## 开发检查清单

### 新增平台适配器时检查:
- [ ] 继承 BaseAIAdapter
- [ ] 实现 getSelectors() 抽象方法
- [ ] 提供平台特定的选择器配置
- [ ] 在 ContentScriptManager 中注册
- [ ] 测试选择器的有效性
- [ ] 添加平台特有的处理逻辑（如需要）
- [ ] 遵循中介者模式，不直接与子模块交互
- [ ] 文件大小不超过 300 行

### 修改选择器时检查:
- [ ] 优先修改平台特定选择器
- [ ] 通用选择器作为降级方案
- [ ] 测试选择器在目标平台的有效性
- [ ] 考虑选择器的稳定性和兼容性
- [ ] 避免过于复杂的CSS选择器
- [ ] 记录选择器变更的原因
