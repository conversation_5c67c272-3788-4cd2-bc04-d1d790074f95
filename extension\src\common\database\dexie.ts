import Dexie, { Table } from 'dexie'
import { ChatHistory, Platform, ChatPrompt } from '@/common/types/database_entity'



export class EchoSyncDatabase extends <PERSON><PERSON> {
  // 定义表
  chatHistory!: Table<ChatHistory>
  chatPrompt!: Table<ChatPrompt>
  platform!: Table<Platform>

  constructor() {
    super('EchoSyncDatabase')
    
    // 定义数据库结构
    this.version(1).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced',
      platform: '++id, name, url, is_delete'
    })

    // 添加索引以提高查询性能
    this.version(2).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced, [platform_id+create_time]',
      platform: '++id, name, url, is_delete'
    }).upgrade(tx => {
      console.log('Upgrading database to version 2...')
    })

    // 添加chat_prompt索引以支持跨平台UID共享
    this.version(3).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced, chat_prompt, [platform_id+create_time]',
      platform: '++id, name, url, is_delete'
    }).upgrade(tx => {
      console.log('Upgrading database to version 3 - adding chat_prompt index...')
    })

    // 添加icon_blob字段支持BLOB存储
    this.version(4).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced, chat_prompt, [platform_id+create_time]',
      platform: '++id, name, url, is_delete'
    }).upgrade(tx => {
      console.log('Upgrading database to version 4 - adding icon_blob support...')
      // 注意：Dexie会自动处理新字段的添加，无需手动迁移
    })

    // 数据结构重构：分离chat_prompt表
    this.version(5).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced, is_answered, [platform_id+create_time]',
      chatPrompt: '++id, chat_prompt, chat_uid, create_time, is_delete, is_synced',
      platform: '++id, name, url, is_delete'
    }).upgrade(async tx => {
      console.log('【EchoSync】Upgrading database to version 5 - restructuring chat data...')
      await this.migrateToVersion5(tx)
    })
  }

  /**
   * 数据迁移到版本5：分离chat_prompt表
   */
  private async migrateToVersion5(tx: any): Promise<void> {
    try {
      console.log('【EchoSync】Starting migration to version 5...')

      // 获取所有现有的聊天历史记录
      const existingChatHistories = await tx.table('chatHistory').toArray()
      console.log(`【EchoSync】Found ${existingChatHistories.length} existing chat history records`)

      // 提取唯一的提示词
      const uniquePromptsMap = new Map<string, {
        chat_prompt: string
        chat_uid: string
        earliest_time: number
      }>()

      for (const history of existingChatHistories) {
        if (history.chat_prompt) {
          const existing = uniquePromptsMap.get(history.chat_prompt)
          if (!existing || history.create_time < existing.earliest_time) {
            uniquePromptsMap.set(history.chat_prompt, {
              chat_prompt: history.chat_prompt,
              chat_uid: history.chat_uid,
              earliest_time: history.create_time
            })
          }
        }
      }

      console.log(`【EchoSync】Extracted ${uniquePromptsMap.size} unique prompts`)

      // 创建chat_prompt记录
      const chatPromptRecords = Array.from(uniquePromptsMap.values()).map(prompt => ({
        chat_prompt: prompt.chat_prompt,
        chat_uid: prompt.chat_uid,
        create_time: prompt.earliest_time,
        is_synced: 0,
        is_delete: 0
      }))

      // 批量插入chat_prompt记录
      await tx.table('chatPrompt').bulkAdd(chatPromptRecords)
      console.log(`【EchoSync】Inserted ${chatPromptRecords.length} chat prompt records`)

      // 更新chat_history记录，添加is_answered字段并移除chat_prompt字段
      const updatedChatHistories = existingChatHistories.map(history => {
        const { chat_prompt, ...historyWithoutPrompt } = history
        return {
          ...historyWithoutPrompt,
          is_answered: history.chat_answer ? 1 : 0
        }
      })

      // 清空并重新插入chat_history记录
      await tx.table('chatHistory').clear()
      await tx.table('chatHistory').bulkAdd(updatedChatHistories)
      console.log(`【EchoSync】Updated ${updatedChatHistories.length} chat history records`)

      console.log('【EchoSync】Migration to version 5 completed successfully')
    } catch (error) {
      console.error('【EchoSync】Migration to version 5 failed:', error)
      throw error
    }
  }

  /**
   * 初始化数据库
   */
  async initialize(): Promise<void> {
    try {
      console.log('【EchoSync】Starting database initialization...')
      await this.open()
      console.log('【EchoSync】Database opened successfully')

      await this.insertDefaultPlatforms()
      console.log('【EchoSync】Default platforms inserted')

      // 验证数据库是否正常工作
      const platformCount = await this.platform.count()
      const chatHistoryCount = await this.chatHistory.count()
      const chatPromptCount = await this.chatPrompt.count()
      console.log('【EchoSync】Database verification - Platforms:', platformCount, 'ChatHistory:', chatHistoryCount, 'ChatPrompt:', chatPromptCount)

      // 验证数据完整性
      await this.validateDataIntegrity()

      console.log('【EchoSync】Dexie database initialized successfully')
    } catch (error) {
      console.error('【EchoSync】Database initialization failed:', error)
      console.error('【EchoSync】Error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      })
      throw error
    }
  }

  /**
   * 验证数据完整性
   */
  private async validateDataIntegrity(): Promise<void> {
    try {
      console.log('【EchoSync】Starting data integrity validation...')

      // 检查chat_uid关联关系
      const chatHistories = await this.chatHistory.toArray()
      const chatPrompts = await this.chatPrompt.toArray()

      const promptUids = new Set(chatPrompts.map(p => p.chat_uid))
      const historyUids = new Set(chatHistories.map(h => h.chat_uid))

      // 检查是否有孤立的历史记录（没有对应的提示词）
      const orphanedHistories = chatHistories.filter(h => !promptUids.has(h.chat_uid))
      if (orphanedHistories.length > 0) {
        console.warn(`【EchoSync】Found ${orphanedHistories.length} orphaned chat histories without corresponding prompts`)
      }

      // 检查是否有未使用的提示词（没有对应的历史记录）
      const unusedPrompts = chatPrompts.filter(p => !historyUids.has(p.chat_uid))
      if (unusedPrompts.length > 0) {
        console.warn(`【EchoSync】Found ${unusedPrompts.length} unused prompts without corresponding histories`)
      }

      // 检查chat_prompt表中是否有重复的提示词
      const promptTexts = chatPrompts.map(p => p.chat_prompt)
      const uniquePromptTexts = new Set(promptTexts)
      if (promptTexts.length !== uniquePromptTexts.size) {
        console.warn('【EchoSync】Found duplicate prompts in chat_prompt table')
      }

      console.log('【EchoSync】Data integrity validation completed')
    } catch (error) {
      console.error('【EchoSync】Data integrity validation failed:', error)
      // 不抛出错误，只记录警告
    }
  }

  /**
   * 插入默认平台数据
   */
  private async insertDefaultPlatforms(): Promise<void> {
    const defaultPlatforms: Platform[] = [
      {
        id: 1,
        name: 'DeepSeek',
        url: 'https://chat.deepseek.com',
        icon: 'https://chat.deepseek.com/favicon.ico',
        is_delete: 0
      },
      {
        id: 2,
        name: 'Kimi',
        url: 'https://kimi.moonshot.cn',
        icon: 'https://kimi.moonshot.cn/favicon.ico',
        is_delete: 0
      },
      {
        id: 3,
        name: 'ChatGPT',
        url: 'https://chat.openai.com',
        icon: 'https://chat.openai.com/favicon.ico',
        is_delete: 0
      },
      {
        id: 4,
        name: 'Claude',
        url: 'https://claude.ai',
        icon: 'https://claude.ai/favicon.ico',
        is_delete: 0
      },
      {
        id: 5,
        name: 'Gemini',
        url: 'https://gemini.google.com',
        icon: 'https://gemini.google.com/favicon.ico',
        is_delete: 0
      }
    ]

    // 逐个检查并插入平台数据，确保ID固定
    for (const platform of defaultPlatforms) {
      const existing = await this.platform.get(platform.id)
      if (!existing) {
        await this.platform.put(platform)
        console.log(`Platform ${platform.name} (ID: ${platform.id}) inserted`)
      }
    }
  }










}

// 导出单例实例
export const dexieDatabase = new EchoSyncDatabase()

