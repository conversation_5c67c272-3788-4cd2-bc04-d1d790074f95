---
type: "development_rules"
description: "UI注入组件开发规则"
---

# UI注入组件开发规则

## 核心设计原则

### 职责分离
- **Inject 模块**: 负责UI的创建、注入、销毁
- **Component 模块**: 负责UI的渲染和样式
- **Adapter**: 负责协调和业务逻辑

### 非侵入性原则
- 不影响原页面的正常功能
- 不破坏原页面的样式和布局
- 提供清晰的视觉反馈
- 使用最高 z-index 确保显示层级

## Inject 组件开发规范

### 基础结构要求
- **位置**: `extension/src/content/inject/`
- **命名**: `ComponentNameInject.ts`
- **依赖**: 通过构造函数接收 BaseAIAdapter 实例
- **组件**: 创建对应的 Component 实例

### 必须实现的方法
- **inject()**: 注入组件到页面DOM
- **findContainer()**: 查找合适的父容器
- **setupEventListeners()**: 设置全局事件监听
- **setupComponentEventListeners()**: 设置组件DOM事件监听
- **destroy()**: 销毁组件和清理资源

### 注入流程规范
1. 检查是否已注入，避免重复注入
2. 调用组件的 render() 方法获取DOM元素
3. 查找合适的父容器（优先特定容器，降级使用body）
4. 将元素添加到父容器
5. 设置事件监听器
6. 标记注入状态

### 容器选择策略
- 优先使用页面特定的容器
- 降级使用 document.body
- 确保容器存在且可访问

## FloatingBubbleInject 专用规则

### 1. 核心功能
- 创建和注入浮动小球
- 处理小球的交互事件
- 管理小球的位置和状态
- 协调历史气泡的显示

### 2. 实现要点
```typescript
// 位置: extension/src/content/inject/FloatingBubbleInject.ts
export class FloatingBubbleInject {
  private component: FloatingBubble
  private historyBubbleInject: HistoryBubbleInject | null = null
  private floatingBubbleDrag: FLoatingBubbleDrag | null = null
  private adapter: BaseAIAdapter
  private hoverTimer: number | null = null

  constructor(adapter: BaseAIAdapter) {
    this.adapter = adapter
    this.component = new FloatingBubble()
    
    this.initializeHistoryBubble()
    this.setupEventListeners()
    this.inject()
  }

  /**
   * 注入浮动小球
   */
  async inject(): Promise<void> {
    const bubble = this.component.render()
    
    if (bubble) {
      // 添加到 DOM
      document.body.appendChild(bubble)
      
      // 初始化拖拽功能
      this.floatingBubbleDrag = new FLoatingBubbleDrag(bubble)
      
      // 设置事件监听
      this.setupBubbleEventListeners()
      
      console.log('【EchoSync】Floating bubble injected successfully')
    }
  }

  /**
   * 设置小球DOM事件监听
   */
  private setupBubbleEventListeners(): void {
    const bubble = this.component.getElement()
    if (!bubble) return

    // 鼠标悬停事件
    bubble.addEventListener('mouseenter', this.handleMouseEnter.bind(this))
    bubble.addEventListener('mouseleave', this.handleMouseLeave.bind(this))

    // 点击事件
    bubble.addEventListener('click', this.handleClick.bind(this))

    // 右键事件
    bubble.addEventListener('contextmenu', this.handleContextMenu.bind(this))
  }

  /**
   * 处理点击事件
   */
  private handleClick(e: MouseEvent): void {
    // 检查是否是拖拽后的点击
    if (this.floatingBubbleDrag && this.floatingBubbleDrag.isDragging()) {
      e.preventDefault()
      return
    }

    // 派发点击事件
    document.dispatchEvent(new CustomEvent(EchoSyncEventEnum.FLOATING_BUBBLE_CLICKED, {
      detail: { position: { x: e.clientX || 0, y: e.clientY || 0 } }
    }))

    // 派发显示提示词事件
    document.dispatchEvent(new CustomEvent(EchoSyncEventEnum.SHOW_STORED_PROMPTS))
  }

  /**
   * 处理鼠标悬停
   */
  private handleMouseEnter(): void {
    this.component.setHoverEffect(true)
    
    // 延迟显示历史气泡
    this.hoverTimer = window.setTimeout(() => {
      this.showHistoryBubble()
    }, 500)
  }

  /**
   * 处理鼠标离开
   */
  private handleMouseLeave(): void {
    this.component.setHoverEffect(false)
    
    // 清除定时器
    if (this.hoverTimer) {
      clearTimeout(this.hoverTimer)
      this.hoverTimer = null
    }

    // 延迟隐藏历史气泡
    setTimeout(() => {
      if (!this.isMouseOverHistoryBubble()) {
        this.historyBubbleInject?.hide()
      }
    }, 300)
  }
}
```

## Component 组件开发规范

### 1. 基础结构
```typescript
// 位置: extension/src/content/components/
// 文件名: ComponentName.ts

export class ComponentName {
  private element: HTMLElement | null = null
  private isRendered: boolean = false

  /**
   * 渲染组件
   */
  render(): HTMLElement | null {
    if (this.isRendered && this.element) {
      return this.element
    }

    this.element = this.createElement()
    this.applyStyles()
    this.isRendered = true
    
    return this.element
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div')
    element.className = 'echosync-component'
    element.innerHTML = this.getTemplate()
    return element
  }

  /**
   * 获取模板
   */
  private getTemplate(): string {
    return `
      <div class="component-content">
        <!-- 组件内容 -->
      </div>
    `
  }

  /**
   * 应用样式
   */
  private applyStyles(): void {
    if (!this.element) return

    // 应用内联样式或CSS类
    Object.assign(this.element.style, {
      position: 'fixed',
      zIndex: '10000',
      // 其他样式
    })
  }

  /**
   * 获取元素引用
   */
  getElement(): HTMLElement | null {
    return this.element
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element)
    }
    this.element = null
    this.isRendered = false
  }
}
```

### 2. FloatingBubble 组件规范
```typescript
// 位置: extension/src/content/components/FloatingBubble.ts
export class FloatingBubble {
  private element: HTMLElement | null = null
  private position: { x: number; y: number } = { x: 0, y: 0 }

  /**
   * 移动到输入框附近
   */
  moveToInputField(inputElement: HTMLElement): void {
    if (!this.element) return

    const rect = inputElement.getBoundingClientRect()
    const newPosition = {
      x: rect.right + 10,
      y: rect.top + rect.height / 2 - 25
    }

    this.setPosition(newPosition)
  }

  /**
   * 设置位置
   */
  private setPosition(position: { x: number; y: number }): void {
    if (!this.element) return

    this.position = position
    this.element.style.transform = `translate(${position.x}px, ${position.y}px)`
  }

  /**
   * 设置悬停效果
   */
  setHoverEffect(isHover: boolean): void {
    if (!this.element) return

    if (isHover) {
      this.element.style.transform += ' scale(1.1)'
    } else {
      this.element.style.transform = this.element.style.transform.replace(' scale(1.1)', '')
    }
  }

  /**
   * 边界回弹
   */
  snapToBoundary(): void {
    if (!this.element) return

    const rect = this.element.getBoundingClientRect()
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    }

    let newX = this.position.x
    let newY = this.position.y

    // 检查边界并调整位置
    if (rect.right > viewport.width) {
      newX = viewport.width - rect.width - 10
    }
    if (rect.left < 0) {
      newX = 10
    }
    if (rect.bottom > viewport.height) {
      newY = viewport.height - rect.height - 10
    }
    if (rect.top < 0) {
      newY = 10
    }

    this.setPosition({ x: newX, y: newY })
  }
}
```

## 拖拽功能规范

### 1. FLoatingBubbleDrag 实现
```typescript
// 位置: extension/src/content/inject/FLoatingBubbleDrag.ts
export class FLoatingBubbleDrag {
  private element: HTMLElement
  private isDragging: boolean = false
  private startPosition: { x: number; y: number } = { x: 0, y: 0 }
  private currentPosition: { x: number; y: number } = { x: 0, y: 0 }

  constructor(element: HTMLElement) {
    this.element = element
    this.setupDragListeners()
    this.loadPosition()
  }

  /**
   * 设置拖拽监听器
   */
  private setupDragListeners(): void {
    this.element.addEventListener('mousedown', this.handleMouseDown.bind(this))
  }

  /**
   * 处理鼠标按下
   */
  private handleMouseDown(e: MouseEvent): void {
    this.isDragging = true
    this.startPosition = { x: e.clientX, y: e.clientY }

    // 添加全局监听器
    document.addEventListener('mousemove', this.handleMouseMove.bind(this))
    document.addEventListener('mouseup', this.handleMouseUp.bind(this))

    e.preventDefault()
  }

  /**
   * 处理鼠标移动
   */
  private handleMouseMove(e: MouseEvent): void {
    if (!this.isDragging) return

    const deltaX = e.clientX - this.startPosition.x
    const deltaY = e.clientY - this.startPosition.y

    this.currentPosition = {
      x: this.currentPosition.x + deltaX,
      y: this.currentPosition.y + deltaY
    }

    this.updateElementPosition()
    this.startPosition = { x: e.clientX, y: e.clientY }
  }

  /**
   * 处理鼠标释放
   */
  private handleMouseUp(): void {
    this.isDragging = false

    // 移除全局监听器
    document.removeEventListener('mousemove', this.handleMouseMove.bind(this))
    document.removeEventListener('mouseup', this.handleMouseUp.bind(this))

    // 保存位置
    this.savePosition()
  }

  /**
   * 更新元素位置
   */
  private updateElementPosition(): void {
    this.element.style.transform = 
      `translate(${this.currentPosition.x}px, ${this.currentPosition.y}px)`
  }

  /**
   * 保存位置到本地存储
   */
  private savePosition(): void {
    localStorage.setItem('echosync-bubble-position', JSON.stringify(this.currentPosition))
  }

  /**
   * 从本地存储加载位置
   */
  private loadPosition(): void {
    const saved = localStorage.getItem('echosync-bubble-position')
    if (saved) {
      try {
        this.currentPosition = JSON.parse(saved)
        this.updateElementPosition()
      } catch (error) {
        console.warn('【EchoSync】Failed to load bubble position:', error)
      }
    }
  }

  /**
   * 检查是否正在拖拽
   */
  isDragging(): boolean {
    return this.isDragging
  }

  /**
   * 销毁拖拽功能
   */
  destroy(): void {
    this.element.removeEventListener('mousedown', this.handleMouseDown.bind(this))
    document.removeEventListener('mousemove', this.handleMouseMove.bind(this))
    document.removeEventListener('mouseup', this.handleMouseUp.bind(this))
  }
}
```

## 样式和CSS规范

### 1. CSS类命名
```css
/* 使用统一的前缀避免冲突 */
.echosync-floating-bubble { }
.echosync-history-bubble { }
.echosync-archive-button { }

/* 状态类 */
.echosync-bubble--hover { }
.echosync-bubble--dragging { }
.echosync-bubble--active { }
```

### 2. 样式隔离
```typescript
// 使用内联样式或CSS-in-JS避免样式冲突
private applyStyles(): void {
  if (!this.element) return

  Object.assign(this.element.style, {
    position: 'fixed',
    zIndex: '2147483647', // 最高层级
    pointerEvents: 'auto',
    userSelect: 'none',
    // 其他样式
  })
}
```

### 3. 响应式设计
```typescript
// 适配不同屏幕尺寸
private adjustForViewport(): void {
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  }

  // 根据视口大小调整组件
  if (viewport.width < 768) {
    // 移动端适配
  } else {
    // 桌面端适配
  }
}
```

## 错误处理和调试

### 1. 注入失败处理
```typescript
async inject(): Promise<void> {
  try {
    const element = this.component.render()
    if (!element) {
      throw new Error('Component render failed')
    }

    const container = this.findContainer()
    if (!container) {
      throw new Error('Container not found')
    }

    container.appendChild(element)
    this.isInjected = true
  } catch (error) {
    console.error('【EchoSync】Injection failed:', this.constructor.name, error)
    // 降级处理或重试
  }
}
```

### 2. 事件处理错误
```typescript
private handleClick(e: MouseEvent): void {
  try {
    // 事件处理逻辑
  } catch (error) {
    console.error('【EchoSync】Click handler error:', error)
    // 防止错误传播
    e.stopPropagation()
  }
}
```

## 开发检查清单

### 新建 Inject 组件时检查:
- [ ] 通过构造函数接收 BaseAIAdapter 实例
- [ ] 实现 inject() 方法
- [ ] 实现 destroy() 方法
- [ ] 创建对应的 Component 类
- [ ] 设置适当的事件监听器
- [ ] 处理注入失败的情况
- [ ] 使用统一的CSS类命名
- [ ] 添加适当的错误处理
- [ ] 文件大小不超过 300 行
- [ ] 遵循命名规范
