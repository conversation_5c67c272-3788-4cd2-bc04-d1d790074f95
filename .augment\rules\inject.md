---
type: "development_rules"
description: "UI注入组件开发规则"
---

# UI注入组件开发规则

## 核心设计原则

### 职责分离
- **Inject 模块**: 负责UI的创建、注入、销毁
- **Component 模块**: 负责UI的渲染和样式
- **Adapter**: 负责协调和业务逻辑

### 非侵入性原则
- 不影响原页面的正常功能
- 不破坏原页面的样式和布局
- 提供清晰的视觉反馈
- 使用最高 z-index 确保显示层级

## Inject 组件开发规范

### 基础结构要求
- **位置**: `extension/src/content/inject/`
- **命名**: `ComponentNameInject.ts`
- **依赖**: 通过构造函数接收 BaseAIAdapter 实例
- **组件**: 创建对应的 Component 实例

### 必须实现的方法
- **inject()**: 注入组件到页面DOM
- **findContainer()**: 查找合适的父容器
- **setupEventListeners()**: 设置全局事件监听
- **setupComponentEventListeners()**: 设置组件DOM事件监听
- **destroy()**: 销毁组件和清理资源

### 注入流程规范
1. 检查是否已注入，避免重复注入
2. 调用组件的 render() 方法获取DOM元素
3. 查找合适的父容器（优先特定容器，降级使用body）
4. 将元素添加到父容器
5. 设置事件监听器
6. 标记注入状态

### 容器选择策略
- 优先使用页面特定的容器
- 降级使用 document.body
- 确保容器存在且可访问

## FloatingBubbleInject 专用规则

### 核心功能
- 创建和注入浮动小球
- 处理小球的交互事件
- 管理小球的位置和状态
- 协调历史气泡的显示

### 实现要点
- 位置: `extension/src/content/inject/FloatingBubbleInject.ts`
- 包含 FloatingBubble 组件实例
- 管理 HistoryBubbleInject 和 FLoatingBubbleDrag 实例
- 处理鼠标悬停、点击、右键等事件
- 支持延迟显示历史气泡功能
- 检查拖拽状态避免误触发点击事件
### 事件处理机制
- 设置鼠标进入、离开、点击、右键等事件监听
- 检查拖拽状态避免误触发点击事件
- 派发自定义事件通知其他组件
- 使用定时器延迟显示/隐藏历史气泡
- 处理悬停效果和位置调整

## Component 组件开发规范

### 基础结构要求
- **位置**: `extension/src/content/components/`
- **命名**: `ComponentName.ts`
- **状态管理**: 维护 element 和 isRendered 状态
- **渲染机制**: 实现 render() 方法返回 DOM 元素
- **样式应用**: 通过 applyStyles() 设置内联样式
- **模板系统**: 通过 getTemplate() 返回 HTML 模板
- **生命周期**: 提供 destroy() 方法清理资源

### FloatingBubble 组件规范
- **位置**: `extension/src/content/components/FloatingBubble.ts`
- **位置管理**: 维护 position 状态，支持位置设置和移动
- **输入框定位**: 提供 moveToInputField() 方法移动到输入框附近
- **悬停效果**: 通过 setHoverEffect() 设置缩放效果
- **边界检测**: 实现 snapToBoundary() 防止超出视口边界
- **变换应用**: 使用 CSS transform 实现位置和效果变化

## 拖拽功能规范

### FLoatingBubbleDrag 实现要求
- **位置**: `extension/src/content/inject/FLoatingBubbleDrag.ts`
- **状态管理**: 维护拖拽状态、起始位置、当前位置
- **事件监听**: 处理鼠标按下、移动、释放事件
- **位置计算**: 根据鼠标移动计算元素新位置
- **位置持久化**: 使用 localStorage 保存和加载位置
- **状态检查**: 提供 isDragging() 方法检查拖拽状态
- **资源清理**: 实现 destroy() 方法移除事件监听器

## 样式和CSS规范

### CSS类命名规范
- 使用统一的 `echosync-` 前缀避免样式冲突
- 组件类: `echosync-floating-bubble`, `echosync-history-bubble`
- 状态类: `echosync-bubble--hover`, `echosync-bubble--dragging`

### 样式隔离策略
- 优先使用内联样式避免样式冲突
- 设置最高 z-index (2147483647) 确保显示层级
- 禁用用户选择和指针事件控制
- 使用 CSS-in-JS 方式应用样式

### 响应式设计要求
- 检测视口尺寸进行适配
- 移动端和桌面端差异化处理
- 动态调整组件大小和位置

## 错误处理和调试

### 注入失败处理策略
- 检查组件渲染是否成功
- 验证容器是否存在
- 使用 try-catch 包装注入逻辑
- 记录详细错误信息便于调试
- 提供降级处理或重试机制

### 事件处理错误管理
- 使用 try-catch 包装事件处理逻辑
- 防止错误向上传播影响页面
- 记录错误信息便于问题排查
- 使用 stopPropagation 阻止事件冒泡

## 开发检查清单

### 新建 Inject 组件时检查:
- [ ] 通过构造函数接收 BaseAIAdapter 实例
- [ ] 实现 inject() 方法
- [ ] 实现 destroy() 方法
- [ ] 创建对应的 Component 类
- [ ] 设置适当的事件监听器
- [ ] 处理注入失败的情况
- [ ] 使用统一的CSS类命名
- [ ] 添加适当的错误处理
- [ ] 文件大小不超过 300 行
- [ ] 遵循命名规范
