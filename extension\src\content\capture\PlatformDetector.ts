import { PlatformConfigList } from '@/content/configs/Consts'
import { PlatformConfig } from '../types/PlatformConfigType'

/**
 * 简化的平台检测器 - 基于域名正则匹配
 */
export class PlatformDetector {
  /**
   * 检测当前平台
   */
  detectCurrentPlatform(): PlatformConfig | null {
    const hostname = window.location.hostname

    for (const config of PlatformConfigList) {
      // 检查主机名匹配
      if (config.patterns.hostname.test(hostname)) {       
          return config
      }
    }

    console.error('【PlatformDetector】No platform detected')
    return null
  }

  /**
   * 静态检测方法
   */
  static detectByUrl(url: string): PlatformConfig | null {
    const urlObj = new URL(url)
    return new PlatformDetector().detectCurrentPlatform()
  }
}
