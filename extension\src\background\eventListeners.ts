/**
 * Background事件监听模块
 * 负责处理Chrome扩展的各种事件监听
 */

import { StorageService } from '@/common/service/storage'
import { MessagingService } from '@/common/service/MessagingService'
import { MessageType } from '@/common/types'
import { databaseConnectionManager } from './databaseConnection'
import { keepAliveManager } from './keepAlive'
import { healthMonitor } from './healthMonitor'

export class EventListeners {
  /**
   * 初始化所有事件监听器
   */
  static initialize(): void {
    this.setupInstallListener()
    this.setupStartupListener()
    this.setupTabUpdateListener()
    this.setupCommandListener()
  }

  /**
   * 扩展安装事件监听
   */
  private static setupInstallListener(): void {
    chrome.runtime.onInstalled.addListener(async (details) => {
      console.log('【Background】扩展安装事件:', details.reason)

      try {
        await this.initializeServiceWorker()

        if (details.reason === 'install') {
          await this.handleFirstInstall()
        }
      } catch (error) {
        console.error('【Background】安装设置失败:', error)
      }
    })
  }

  /**
   * Service Worker启动事件监听
   */
  private static setupStartupListener(): void {
    chrome.runtime.onStartup.addListener(async () => {
      console.log('【Background】Service Worker启动')
      try {
        await this.initializeServiceWorker()
      } catch (error) {
        console.error('【Background】启动初始化失败:', error)
      }
    })
  }

  /**
   * 标签页更新事件监听
   */
  private static setupTabUpdateListener(): void {
    chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && tab.url) {
        await this.handleTabComplete(tabId, tab.url)
      }
    })
  }

  /**
   * 快捷键命令事件监听
   */
  private static setupCommandListener(): void {
    chrome.commands.onCommand.addListener(async (command) => {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (!tab.id) return

      await this.handleCommand(command, tab.id)
    })
  }

  /**
   * 初始化Service Worker
   */
  private static async initializeServiceWorker(): Promise<void> {
    console.log('【Background】初始化Service Worker')

    try {
      // 初始化数据库连接管理器
      const dbSuccess = await databaseConnectionManager.initialize()
      if (!dbSuccess) {
        throw new Error('Database initialization failed')
      }
      console.log('【Background】✓ 数据库初始化成功')

      // 初始化保活管理器
      await keepAliveManager.initialize()
      console.log('【Background】✓ 保活管理器初始化成功')

      // 启动健康监控
      healthMonitor.startPeriodicHealthCheck()
      console.log('【Background】✓ 健康监控启动成功')

      // 在开发环境中启动定期健康检查
      if (process.env.NODE_ENV === 'development') {
        healthMonitor.startPeriodicHealthCheck()
        console.log('【Background】✓ 开发环境健康检查启动')
      }

      console.log('【Background】Service Worker初始化完成')
    } catch (error) {
      console.error('【Background】Service Worker初始化失败:', error)
      throw error
    }
  }

  /**
   * 处理首次安装
   */
  private static async handleFirstInstall(): Promise<void> {
    console.log('【Background】处理首次安装')

    // 设置默认配置
    const settings = await StorageService.getSettings()
    await StorageService.saveSettings(settings)

    // 打开欢迎页面
    chrome.tabs.create({
      url: chrome.runtime.getURL('options/index.html?welcome=true')
    })
  }

  /**
   * 处理标签页加载完成
   */
  private static async handleTabComplete(tabId: number, url: string): Promise<void> {
    const supportedDomains = [
      'chat.openai.com',
      'chat.deepseek.com', 
      'claude.ai',
      'gemini.google.com',
      'kimi.moonshot.cn',
      'www.kimi.com'  // 添加Kimi的新域名
    ]

    const isSupported = supportedDomains.some(domain => url.includes(domain))

    if (isSupported) {
      try {
        await chrome.scripting.executeScript({
          target: { tabId },
          files: ['src/content/index.js']
        })
        console.log('【Background】Content script注入成功:', url)
      } catch (error) {
        console.error('【Background】Content script注入失败:', error)
      }
    }
  }

  /**
   * 处理快捷键命令
   */
  private static async handleCommand(command: string, tabId: number): Promise<void> {
    console.log('【Background】处理快捷键命令:', command)

    try {
      switch (command) {
        case 'open-popup':
          chrome.action.openPopup()
          break

        case 'quick-sync':
          await MessagingService.sendToContentScript(tabId, MessageType.CAPTURE_PROMPT, {})
          break

        default:
          console.warn('【Background】未知的快捷键命令:', command)
      }
    } catch (error) {
      console.error('【Background】快捷键命令处理失败:', error)
    }
  }
}
