import { ArchiveButton } from '../components/ArchiveButton'
import { BaseAIAdapter } from '../adapters/BaseAIAdapter'
import { chatHistoryDatabaseProxy, platformDatabaseProxy } from '@/common/service/databaseProxy'
import { DOMUtils } from '../utils/DOMUtils'
import { EchoSyncEventEnum } from '../configs/DOMEnum'

/**
 * 存档按钮注入逻辑
 * 负责与页面元素交互、事件监听和业务逻辑处理
 */
export class ArchiveButtonInject {
  public component: ArchiveButton  // 改为public，允许外部访问
  private currentPromptId: string = ''
  private archivedPromptIds: Set<string> = new Set()
  private archivedPrompt: string = ''
  private adapter: BaseAIAdapter = null
  private resizeObserver: ResizeObserver | null = null
  private resizeTimeout: NodeJS.Timeout | null = null

  constructor(adapter) {
    this.adapter = adapter
    this.component = new ArchiveButton()
    this.generateNewPromptId() //TODO 整理提示词id
    this.setupEventListeners()
    this.inject()
  }

  /**
   * 注入存档按钮到页面
   */
  async inject(): Promise<void> {
    if (!this.adapter) {
      console.warn('【EchoSync】ArchiveButtonInject: No adapter instance available')
      return
    }

    // 渲染UI组件
    const buttonElement = this.component.render()
    document.body.appendChild(buttonElement)

    // 设置点击事件
    this.component.onClick(() => {
      this.archiveCurrentPrompt()
    })

    // 初始定位
    this.repositionButton()
  }


  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听输入框变化事件
    document.addEventListener(EchoSyncEventEnum.INPUT_CHANGED, (e: CustomEvent) => {
      const value = e.detail.value
      this.updateButtonState(value)
    })
  }

  /**
   * 重新定位按钮
   */
  private repositionButton(): void {
    if (!this.adapter) return
    // 直接查找输入元素
    const inputElement = this.adapter.getInputCapture().getInputElement()
    this.positionButton(inputElement)
  }

  /**
   * 定位存档按钮
   */
  private positionButton(inputContainer: HTMLElement): void {
    const updatePosition = () => {
      const rect = inputContainer.getBoundingClientRect()
      const buttonSize = 25
      const margin = 8

      let left = rect.right + margin
      let top = rect.bottom - buttonSize

      // 边界检查
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight

      left = Math.max(margin, Math.min(left, windowWidth - buttonSize - margin))
      top = Math.max(margin, Math.min(top, windowHeight - buttonSize - margin))

      this.component.updatePosition(left, top)
    }

    updatePosition()

    // 防抖处理
    const debouncedUpdate = () => {
      if (this.resizeTimeout) {
        clearTimeout(this.resizeTimeout)
      }
      this.resizeTimeout = setTimeout(updatePosition, 100)
    }

    window.addEventListener('resize', debouncedUpdate)
    window.addEventListener('scroll', debouncedUpdate)

    // 观察容器变化
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
    this.resizeObserver = new ResizeObserver(debouncedUpdate)
    this.resizeObserver.observe(inputContainer)

    if (inputContainer.parentElement) {
      this.resizeObserver.observe(inputContainer.parentElement)
    }
  }

  /**
   * 更新按钮状态
   */
  private updateButtonState(inputValue?: string): void {
    let content = inputValue
    const hasContent = (content || '').trim().length > 0
    // 检查是否已存档
    const isArchived = this.archivedPromptIds.has(this.currentPromptId)


    if (hasContent && content !== this.archivedPrompt) {
      // 有内容且未存档
      this.component.showWithGlow()
    } else if (isArchived) {
      // 已存档
      this.component.showArchivedState()
    } else {
      // 无内容或已存档
      this.component.hide()
    }
  }

  /**
   * 存档当前提示词
   */
  private async archiveCurrentPrompt(): Promise<void> {
    if (!this.adapter) {
      console.error('【EchoSync】ArchiveButtonInject: No adapter instance available')
      return
    }

    const inputPrompt = this.adapter.getInputCapture().getCurrentInput()
    if (!inputPrompt || inputPrompt.trim().length === 0) {
      console.warn('【EchoSync】ArchiveButtonInject: Input is empty, cannot archive')
      return
    }

    try {
      const platformResult = this.adapter.getCurrentPlatform()
      const archiveData = {
        chat_prompt: inputPrompt,
        chat_uid: this.currentPromptId,
        platform_id: platformResult?.id,
        create_time: Date.now()
      }

      const result = await chatHistoryDatabaseProxy.create(archiveData)

      if (result.success) {
        this.archivedPromptIds.add(this.currentPromptId)
        this.component.showArchivedState()
        this.component.showArchiveAnimation()
        this.archivedPrompt = inputPrompt
        console.log('【EchoSync】Prompt archived successfully:', result.data)

      } else {
        console.error('【EchoSync】Archive failed:', result.error)
      }
    } catch (error) {
      console.error('【EchoSync】Archive prompt error:', error)
    }
  }

  /**
   * 生成新的提示词ID
   */
  private generateNewPromptId(): void {
    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substring(2, 8)
    this.currentPromptId = `prompt-${timestamp}-${randomStr}`
  }

  /**
   * 获取当前提示词ID
   */
  getCurrentPromptId(): string {
    return this.currentPromptId
  }

  /**
   * 设置当前提示词ID
   */
  setCurrentPromptId(chatUid: string): void {
    this.currentPromptId = chatUid
  }

  /**
   * 标记为已存档状态
   */
  markAsArchived(): void {
    this.archivedPromptIds.add(this.currentPromptId)
    this.component.showArchivedState()
  }

  /**
   * 销毁注入器
   */
  destroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
      this.resizeObserver = null
    }

    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout)
      this.resizeTimeout = null
    }

    this.component.destroy()


    // 清理数据
    this.archivedPromptIds.clear()
    this.currentPromptId = ''
    this.adapter = null
  }
}
