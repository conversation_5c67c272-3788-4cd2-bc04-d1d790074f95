import { BaseCapture } from './BaseCapture'
import { BaseAIAdapter } from '../adapters/BaseAIAdapter'
import { DOMUtils } from '../utils/DOMUtils'

/**
 * 输入框捕捉类
 * 重构后通过 adapter 获得选择器，再获得元素
 */
export class InputCapture extends BaseCapture {

  // 输入元素
  private inputElement: HTMLElement | null = null
    // 发送按钮容器
  private sendButtonElement: HTMLElement | null = null

  private lastInputValue: string = ''
  private adapter: BaseAIAdapter | null = null

  constructor(adapter: BaseAIAdapter) {
    super()
    this.adapter = adapter
    this.captureElement()
    this.initEventListener()
  }

  /**
   * 获得input元素
   */
  public captureElement(): HTMLElement | null {
    if (this.inputElement == null) {
      this.inputElement = DOMUtils.findElement(this.adapter.mergedSelectors?.inputField)
    }
    if (this.sendButtonElement == null) {
      this.sendButtonElement = DOMUtils.findElement(this.adapter.mergedSelectors?.sendButton)
    }
    return this.inputElement
  }

  /**
   * 初始化对ui事件的监听
   */
  protected initEventListener(): void {
    this.setupFocusListener()

    this.setupSendListener(this.adapter.mergedSelectors.sendButton)
  }

  /**
   * 设置输入框聚焦监听
   */
  private setupFocusListener(): void {
    const inputField = this.inputElement
    if (!inputField) {
      console.warn('【EchoSync】Input field not found, cannot set up focus listener')
      return
    }

    inputField.addEventListener('focusin', (e) => {
      const target = e.target as HTMLElement
      console.log('【EchoSync】Input element focused via focusin:', target)
      // 设置输入监听
      this.setupInputMonitoring(inputField)
      // 触发输入框聚焦事件
      document.dispatchEvent(new CustomEvent('echosync:input-focused', {
        detail: { inputElement: target }
      }))

      console.log('【EchoSync】Input focus listener set up')
    })
  }

  /**
   * 设置输入监听
   */
  private setupInputMonitoring(inputElement: HTMLElement): void {
    if (!inputElement) return

    // 监听输入变化
    const handleInput = () => {
      const currentValue = this.getCurrentInput()
      if (currentValue !== this.lastInputValue) {
        this.lastInputValue = currentValue

        // 触发输入变化事件
        document.dispatchEvent(new CustomEvent('echosync:input-changed', {
          detail: { value: currentValue }
        }))
      }
    }

    this.inputElement.addEventListener('input', handleInput)
    this.inputElement.addEventListener('change', handleInput)
    this.inputElement.addEventListener('keyup', handleInput)
  }

  /**
   * 获取当前输入内容
   */
  private getCurrentInput(): string {
    if (!this.inputElement) return ''
    
    // 获取输入框的值
    const value = (this.inputElement as HTMLInputElement).value || 
                 (this.inputElement as HTMLTextAreaElement).value || 
                 this.inputElement.textContent || ''
                 
    return value.trim()
  }

  /**
   * 设置发送监听
   */
  setupSendListener(selectors: string[]): void {
    const sendButton = this.sendButtonElement as HTMLButtonElement;

    // 监听键盘事件（Enter键）
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey && this.inputElement &&
        document.activeElement === this.inputElement) {

        // 检查是否可以发送
        if (sendButton && !sendButton.disabled ) {
          console.log('【EchoSync】Send triggered by Enter key')
          this.handleSendEvent()
        }
      }
    })

    // 监听发送按钮点击
    if (sendButton) {
      sendButton.addEventListener('click', (e) => {
        this.handleSendEvent()
      })
      console.log('【EchoSync】Send listener set up')
    } else {
      console.warn('【EchoSync】Send button not found, cannot set up click listener')
    }
  }


  /**
   * 处理发送事件
   */
  private async handleSendEvent(): Promise<void> {
    console.log('【EchoSync】InputManager handleSendEvent triggered')

    // 获取发送前的输入内容
    const promptContent = this.getCurrentInput()
    console.log('【EchoSync】Captured prompt content:', promptContent)

    // 触发发送前捕捉事件，让AIAdapter处理存档
    if (promptContent && promptContent.trim().length > 0) {
      document.dispatchEvent(new CustomEvent('echosync:prompt-send', {
        detail: { prompt: promptContent }
      }))
    }

    // 延迟检查是否有新消息出现
    setTimeout(() => {
      console.log('【EchoSync】Dispatching check-new-message event')
      document.dispatchEvent(new CustomEvent('echosync:check-new-message'))
    }, 1000)
  }


  /**
   * 获取最后输入值
   */
  getLastInputValue(): string {
    return this.lastInputValue
  }

  /**
   * 销毁
   */
  destroy(): void {
    this.inputElement = null
    this.sendButtonElement = null
    this.lastInputValue = ''
  }
}
