# 页面元素捕捉流程图

本文档详细描述了 Content Script 如何捕捉页面元素（如输入框组件）的完整流程。

## 一、整体捕捉流程

```mermaid
sequenceDiagram
    participant CSM as ContentScriptManager
    participant BA as BaseAIAdapter
    participant IC as InputCapture
    participant D<PERSON> as DOMUtils
    participant DOM as Page DOM

    CSM->>BA: new ChatGPTAdapter() 等
    BA->>BA: mergeSelectors()
    BA->>IC: new InputCapture(adapter)
    IC->>IC: captureElement()
    IC->>DU: DOMUtils.findElement(selectors)
    DU->>DOM: document.querySelector
    DOM-->>DU: 返回元素引用
    DU-->>IC: 返回找到的元素
    IC->>IC: initEventListener()
    IC->>IC: setupFocusListener()

    Note over IC,DOM: 持续监听页面事件
    DOM->>IC: 触发 focusin 事件
    IC->>IC: setupInputMonitoring()
    DOM->>IC: 触发 input/change/keyup 事件
    IC->>DOM: 派发 echosync:input-changed 事件
```

## 二、详细捕捉流程图

```mermaid
flowchart TD
    A[Content Script 启动] --> B[创建 BaseAIAdapter]
    B --> C[获取平台配置]
    C --> D[合并选择器配置]
    
    D --> E[初始化 InputCapture]
    E --> F[开始元素捕捉流程]
    
    F --> G[adapter.mergedSelectors]
    G --> H[DOMUtils.findElement 查找]

    H --> I[查找输入框元素]
    I --> I1[DOMUtils.findElement(inputField)]
    I1 --> I2{找到元素?}
    I2 -->|是| J[setupFocusListener]
    I2 -->|否| K[console.warn 查找失败]

    H --> L[查找发送按钮元素]
    L --> L1[DOMUtils.findElement(sendButton)]
    L1 --> L2{找到元素?}
    L2 -->|是| M[setupSendListener]
    L2 -->|否| N[console.warn 查找失败]

    J --> O[addEventListener focusin]
    M --> P[addEventListener click/keydown]

    O --> Q[setupInputMonitoring]
    P --> R[handleSendEvent]

    Q --> S[监听 input/change/keyup]
    R --> T[派发 echosync:prompt-send]

    K --> U[部分功能不可用]
    N --> U
    S --> V[捕捉流程完成]
    T --> V
    U --> V
```

## 三、选择器优先级查找流程

```mermaid
flowchart TD
    A[开始查找元素] --> B[获取选择器数组]
    B --> C[按优先级排序]
    
    C --> D[遍历选择器数组]
    D --> E[使用当前选择器查找]
    E --> F[document.querySelector/querySelectorAll]
    
    F --> G{找到元素?}
    G -->|是| H[验证元素有效性]
    G -->|否| I[尝试下一个选择器]
    
    H --> J{元素可见且可用?}
    J -->|是| K[返回找到的元素]
    J -->|否| I
    
    I --> L{还有更多选择器?}
    L -->|是| D
    L -->|否| M[查找失败]
    
    K --> N[记录成功的选择器]
    M --> O[记录查找失败日志]
    
    N --> P[元素查找完成]
    O --> P
```

## 四、输入框捕捉详细流程

```mermaid
sequenceDiagram
    participant IC as InputCapture
    participant DOM as Page DOM
    participant A as BaseAIAdapter
    participant MO as MutationObserver

    IC->>DOM: 查找输入框元素
    Note over IC,DOM: 使用选择器: ['#prompt-textarea', 'textarea[placeholder*="Message"]', ...]
    
    DOM-->>IC: 返回输入框元素
    IC->>IC: 验证元素有效性
    
    alt 元素有效
        IC->>DOM: 添加事件监听器
        Note over IC,DOM: 监听: input, focus, blur, keydown 事件
        IC->>MO: 设置 DOM 变化监听
        IC->>A: 通知输入框捕捉成功
        
        loop 持续监听
            DOM->>IC: 触发 input 事件
            IC->>IC: 获取输入内容
            IC->>A: 通知输入内容变化
            A->>A: 处理输入变化逻辑
            
            DOM->>IC: 触发 focus 事件
            IC->>A: 通知输入框获得焦点
            
            DOM->>IC: 触发 blur 事件
            IC->>A: 通知输入框失去焦点
            
            DOM->>IC: 触发 keydown 事件
            IC->>IC: 检查特殊按键 (Enter, Ctrl+Enter)
            IC->>A: 通知特殊按键事件
        end
        
        MO->>IC: DOM 结构变化
        IC->>IC: 重新验证元素有效性
        alt 元素仍然有效
            IC->>IC: 继续监听
        else 元素失效
            IC->>IC: 重新查找输入框
        end
        
    else 元素无效
        IC->>A: 通知输入框捕捉失败
        IC->>IC: 启动重试机制
    end
```

## 五、发送按钮捕捉流程

```mermaid
flowchart TD
    A[开始捕捉发送按钮] --> B[获取发送按钮选择器]
    B --> C[查找按钮元素]
    
    C --> D{找到按钮?}
    D -->|否| E[记录查找失败]
    D -->|是| F[验证按钮状态]
    
    F --> G{按钮可点击?}
    G -->|否| H[等待按钮可用]
    G -->|是| I[添加点击监听]
    
    I --> J[监听按钮状态变化]
    J --> K[设置 MutationObserver]
    K --> L[监听按钮属性变化]
    
    L --> M[捕捉完成]
    E --> N[启动重试机制]
    H --> O[定时检查按钮状态]
    
    O --> P{按钮变为可用?}
    P -->|是| I
    P -->|否| Q{超时?}
    Q -->|否| O
    Q -->|是| E
    
    N --> R{重试次数超限?}
    R -->|否| C
    R -->|是| S[放弃捕捉]
```

## 六、动态页面监听流程

```mermaid
sequenceDiagram
    participant IC as InputCapture
    participant MO as MutationObserver
    participant DOM as Page DOM
    participant A as BaseAIAdapter

    IC->>MO: 创建 MutationObserver
    IC->>MO: 配置监听选项
    Note over IC,MO: {childList: true, subtree: true, attributes: true}
    
    IC->>DOM: 开始监听 document.body
    MO->>DOM: observer.observe(document.body)
    
    loop 页面动态变化
        DOM->>MO: DOM 结构发生变化
        MO->>IC: 触发 mutation 回调
        IC->>IC: 分析变化类型
        
        alt 新增节点
            IC->>IC: 检查是否包含目标元素
            IC->>IC: 重新查找丢失的元素
        else 删除节点
            IC->>IC: 检查监听的元素是否被删除
            IC->>IC: 清理失效的事件监听
        else 属性变化
            IC->>IC: 检查元素状态变化
            IC->>IC: 更新元素监听状态
        end
        
        IC->>A: 通知页面变化
        A->>A: 更新 UI 组件状态
    end
    
    Note over IC,MO: 页面卸载时清理
    IC->>MO: observer.disconnect()
    IC->>DOM: 移除所有事件监听器
```

## 七、错误处理和重试机制

```mermaid
flowchart TD
    A[元素捕捉失败] --> B{失败原因分析}
    
    B -->|元素不存在| C[页面未完全加载]
    B -->|元素被隐藏| D[元素状态异常]
    B -->|选择器失效| E[页面结构变化]
    B -->|权限问题| F[安全策略限制]
    
    C --> G[等待页面加载完成]
    G --> H[重新查找元素]
    
    D --> I[等待元素状态恢复]
    I --> J[定时检查元素可见性]
    J --> K{元素可见?}
    K -->|是| H
    K -->|否| L{超时?}
    L -->|否| J
    L -->|是| M[使用降级选择器]
    
    E --> N[尝试通用选择器]
    N --> O{找到元素?}
    O -->|是| P[更新选择器优先级]
    O -->|否| Q[记录页面结构变化]
    
    F --> R[降级到基础功能]
    R --> S[禁用相关功能]
    
    H --> T{重试成功?}
    T -->|是| U[恢复正常监听]
    T -->|否| V{重试次数超限?}
    V -->|否| W[延迟后重试]
    V -->|是| X[放弃捕捉]
    
    P --> U
    Q --> X
    S --> Y[部分功能可用]
    W --> H
    X --> Z[记录最终失败]
    
    U --> AA[捕捉流程完成]
    Y --> AA
    Z --> AA
```

## 八、性能优化策略

### 8.1 选择器优化

```mermaid
flowchart TD
    A[选择器性能优化] --> B[缓存查找结果]
    A --> C[选择器优先级排序]
    A --> D[避免复杂选择器]
    
    B --> B1[成功的选择器优先使用]
    B --> B2[缓存元素引用]
    B --> B3[定期清理无效缓存]
    
    C --> C1[平台特定选择器优先]
    C --> C2[简单选择器优先]
    C --> C3[ID选择器 > Class选择器 > 属性选择器]
    
    D --> D1[避免深层嵌套选择器]
    D --> D2[避免通配符选择器]
    D --> D3[使用具体的属性选择器]
```

### 8.2 监听优化

```mermaid
flowchart TD
    A[监听性能优化] --> B[事件委托]
    A --> C[防抖处理]
    A --> D[监听范围限制]
    
    B --> B1[在父容器上监听]
    B --> B2[减少事件监听器数量]
    
    C --> C1[输入事件防抖]
    C --> C2[DOM变化防抖]
    C --> C3[避免频繁回调]
    
    D --> D1[只监听必要的DOM区域]
    D --> D2[限制MutationObserver范围]
    D --> D3[及时移除无用监听]
```

## 九、最佳实践

### 9.1 选择器设计原则
1. **特异性优先**: 平台特定选择器优先于通用选择器
2. **稳定性考虑**: 优先使用稳定的属性（如data-*属性）
3. **性能考虑**: 避免复杂的CSS选择器
4. **兼容性**: 考虑不同版本页面的兼容性

### 9.2 监听策略
1. **延迟初始化**: 页面完全加载后再开始捕捉
2. **优雅降级**: 关键元素找不到时提供替代方案
3. **资源清理**: 页面卸载时及时清理监听器
4. **错误隔离**: 单个元素捕捉失败不影响其他功能

### 9.3 实际性能特点
- 元素查找: 基于 DOMUtils.findElement 的简单查询
- 事件监听: 原生 addEventListener，性能开销最小
- 内存占用: 仅保存必要的元素引用
- 错误处理: 简单的 console.warn 记录
