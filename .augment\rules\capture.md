---
type: "agent_requested"
description: "页面元素捕捉组件开发规则"
---
# 页面元素捕捉规则

## 核心设计原则

### 职责分离
- **Capture 模块**: 只观察页面元素，不修改DOM
- **Inject 模块**: 只修改DOM，不观察页面变化
- **Adapter**: 协调两者，处理业务逻辑

### 中介者模式
- 所有 Capture 组件必须通过 BaseAIAdapter 进行通信
- 严禁 Capture 组件间的直接交互
- 使用自定义事件进行组件间通信

## Capture 组件开发规范

### 基础结构要求
- **位置**: `extension/src/content/capture/`
- **命名**: `ComponentNameCapture.ts`
- **继承**: 继承 BaseCapture 或遵循相同模式
- **依赖**: 通过构造函数接收 BaseAIAdapter 实例

### 必须实现的方法
- **captureElement()**: 查找并返回目标元素
- **initEventListener()**: 初始化事件监听
- **destroy()**: 清理资源和事件监听器

### 选择器使用规则
- 必须使用 DOMUtils.findElement 方法查找元素
- 使用 adapter.mergedSelectors 获取合并后的选择器
- 选择器优先级：平台特定 > 通用选择器

### 事件监听规范
- 使用原生 addEventListener 进行事件监听
- 记录所有事件监听器以便清理
- 通过自定义事件与其他组件通信
- 事件命名使用 'echosync:' 前缀

## InputCapture 专用规则

### 核心职责
- 查找输入框和发送按钮元素
- 监听输入内容变化
- 监听发送事件（Enter键、按钮点击）
- 捕捉用户输入内容

### 监听事件类型
- **focusin**: 输入框获得焦点
- **input/change/keyup**: 输入内容变化
- **keydown**: 特殊按键（Enter、Ctrl+Enter）
- **click**: 发送按钮点击

### 自定义事件派发
- **echosync:input-focused**: 输入框获得焦点
- **echosync:input-changed**: 输入内容变化
- **echosync:prompt-send**: 用户发送提示词
- **echosync:check-new-message**: 检查新消息

## DOMUtils 工具规范

### 元素查找方法
- **findElement()**: 按优先级查找元素
- **isElementVisible()**: 检查元素可见性
- **waitForElement()**: 等待元素出现

### 查找策略
- 按选择器数组顺序依次尝试
- 检查元素可见性和可用性
- 记录查找失败的选择器

## 自定义事件规范

### 事件命名规则
- 使用 'echosync:' 前缀
- 使用 kebab-case 命名
- 事件名称要清晰表达意图

### 事件数据格式
- 使用 CustomEvent 的 detail 属性传递数据
- 包含时间戳等元数据
- 数据结构要简洁明确
- 事件枚举存放到DOMEnum.ts中的EchoSyncEventEnum


## 错误处理规范

### 元素查找失败
- 使用 console.warn 记录警告
- 不阻断其他功能的执行
- 提供降级处理方案

### 事件监听失败
- 检查元素有效性
- 记录错误信息
- 跳过失败的监听器

## 性能优化规则

### 避免频繁查询
- 缓存查找结果
- 使用防抖处理频繁事件
- 及时清理无用的监听器

### 内存管理
- 组件销毁时清理所有引用
- 移除所有事件监听器
- 清理定时器和异步操作

## 开发检查清单

### 新建 Capture 组件时检查
- [ ] 继承 BaseCapture 或遵循相同模式
- [ ] 通过构造函数接收 BaseAIAdapter 实例
- [ ] 实现 captureElement() 方法
- [ ] 实现 initEventListener() 方法
- [ ] 实现 destroy() 方法
- [ ] 使用 DOMUtils.findElement 查找元素
- [ ] 通过自定义事件与其他组件通信
- [ ] 添加适当的错误处理和日志
- [ ] 文件大小不超过 300 行
- [ ] 遵循命名规范
