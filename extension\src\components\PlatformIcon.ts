import { Platform } from '@/common/types/database_entity'

export interface PlatformIconOptions {
  size?: number
  showTooltip?: boolean
  fallbackIcon?: string
  cacheTimeout?: number
}

export class PlatformIcon {
  private iconCache: Map<string, string> = new Map()
  private loadingSet: Set<string> = new Set()
  private options: Required<PlatformIconOptions>

  constructor(options: PlatformIconOptions = {}) {
    this.options = {
      size: options.size || 20,
      showTooltip: options.showTooltip !== false,
      fallbackIcon: options.fallbackIcon || this.getDefaultIcon(),
      cacheTimeout: options.cacheTimeout || 24 * 60 * 60 * 1000 // 24小时
    }

    this.addStyles()
  }

  /**
   * 添加样式
   */
  private addStyles(): void {
    if (document.getElementById('echosync-platform-icon-styles')) return

    const style = document.createElement('style')
    style.id = 'echosync-platform-icon-styles'
    style.textContent = `
      .echosync-platform-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        overflow: hidden;
        background: rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
        position: relative;
      }

      .echosync-platform-icon:hover {
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      .echosync-platform-icon img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
      }

      .echosync-platform-icon-fallback {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 600;
        color: #6b7280;
        background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
      }

      .echosync-platform-icon-loading {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(139, 92, 246, 0.1);
      }

      .echosync-platform-icon-loading::after {
        content: '';
        width: 12px;
        height: 12px;
        border: 2px solid #8b5cf6;
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: echosync-icon-spin 1s linear infinite;
      }

      @keyframes echosync-icon-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .echosync-platform-tooltip {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s ease;
        z-index: 10003;
        margin-bottom: 4px;
      }

      .echosync-platform-tooltip::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 4px solid transparent;
        border-top-color: rgba(0, 0, 0, 0.8);
      }

      .echosync-platform-icon:hover .echosync-platform-tooltip {
        opacity: 1;
      }

      .echosync-platform-icons-group {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .echosync-platform-icons-group .echosync-platform-icon {
        border: 1px solid rgba(255, 255, 255, 0.5);
      }
    `
    document.head.appendChild(style)
  }

  /**
   * 获取默认图标SVG
   */
  private getDefaultIcon(): string {
    return `data:image/svg+xml,${encodeURIComponent(`
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="12" r="10"/>
        <path d="M8 14s1.5 2 4 2 4-2 4-2"/>
        <line x1="9" y1="9" x2="9.01" y2="9"/>
        <line x1="15" y1="9" x2="15.01" y2="9"/>
      </svg>
    `)}`
  }

  /**
   * 创建平台图标元素
   */
  public createIcon(platform: Platform, options: Partial<PlatformIconOptions> = {}): HTMLElement {
    const finalOptions = { ...this.options, ...options }
    
    const container = document.createElement('div')
    container.className = 'echosync-platform-icon'
    container.style.width = `${finalOptions.size}px`
    container.style.height = `${finalOptions.size}px`
    container.dataset.platformId = platform.id.toString()
    container.dataset.platformName = platform.name

    // 添加工具提示
    if (finalOptions.showTooltip) {
      const tooltip = document.createElement('div')
      tooltip.className = 'echosync-platform-tooltip'
      tooltip.textContent = platform.name
      container.appendChild(tooltip)
    }

    // 加载图标
    this.loadIcon(platform, container, finalOptions)

    return container
  }

  /**
   * 加载图标
   */
  private async loadIcon(
    platform: Platform,
    container: HTMLElement,
    options: Required<PlatformIconOptions>
  ): Promise<void> {
    const cacheKey = `${platform.id}-${platform.name}`

    // 显示加载状态
    this.showLoading(container)

    try {
      // 检查缓存
      if (this.iconCache.has(cacheKey)) {
        const cachedUrl = this.iconCache.get(cacheKey)!
        this.showIcon(container, cachedUrl, platform.name, options)
        return
      }

      // 检查是否正在加载
      if (this.loadingSet.has(cacheKey)) {
        return
      }

      this.loadingSet.add(cacheKey)

      // 优先使用Base64缓存数据
      let iconUrl: string
      console.log(`【PlatformIcon-${platform.name}】检查icon_base64:`, {
        hasIconBase64: !!platform.icon_base64,
        base64Length: platform.icon_base64?.length || 0
      })

      if (platform.icon_base64 && platform.icon_base64.startsWith('data:image/')) {
        //console.log(`【PlatformIcon-${platform.name}】使用Base64缓存数据，长度:`, platform.icon_base64.length)
        iconUrl = platform.icon_base64
      } else {
        // Fallback到原有逻辑
        console.log(`【PlatformIcon-${platform.name}】Base64缓存无效，使用fallback逻辑`)
        iconUrl = await this.resolveIconUrl(platform)

        if (iconUrl) {
          // 验证图标是否可访问
          const isValid = await this.validateIcon(iconUrl)
          if (!isValid) {
            iconUrl = options.fallbackIcon
          }
        } else {
          iconUrl = options.fallbackIcon
        }
      }

      // 缓存结果
      this.iconCache.set(cacheKey, iconUrl)

      // 显示图标
      this.showIcon(container, iconUrl, platform.name, options)

    } catch (error) {
      console.error('Failed to load platform icon:', error)
      this.showIcon(container, options.fallbackIcon, platform.name, options)
    } finally {
      this.loadingSet.delete(cacheKey)
    }
  }

  /**
   * 解析图标URL
   */
  private async resolveIconUrl(platform: Platform): Promise<string> {
    // 如果已有图标URL，直接使用
    if (platform.icon) {
      return platform.icon
    }

    // 尝试从平台URL获取favicon
    try {
      const url = new URL(platform.url)
      return `${url.origin}/favicon.ico`
    } catch (error) {
      console.error('Invalid platform URL:', platform.url)
      return ''
    }
  }

  /**
   * 验证图标是否可访问
   */
  private async validateIcon(iconUrl: string): Promise<boolean> {
    try {
      const response = await fetch(iconUrl, { 
        method: 'HEAD',
        mode: 'no-cors'
      })
      return response.ok || response.type === 'opaque'
    } catch (error) {
      return false
    }
  }

  /**
   * 显示加载状态
   */
  private showLoading(container: HTMLElement): void {
    container.innerHTML = '<div class="echosync-platform-icon-loading"></div>'
  }

  /**
   * 显示图标
   */
  private showIcon(
    container: HTMLElement, 
    iconUrl: string, 
    altText: string,
    options: Required<PlatformIconOptions>
  ): void {
    // 清除加载状态
    const loading = container.querySelector('.echosync-platform-icon-loading')
    if (loading) {
      loading.remove()
    }

    if (iconUrl === options.fallbackIcon || iconUrl.startsWith('data:')) {
      // 显示SVG图标或回退图标
      if (iconUrl.startsWith('data:image/svg+xml')) {
        const img = document.createElement('img')
        img.src = iconUrl
        img.alt = altText
        img.onerror = () => this.showFallback(container, altText)
        container.appendChild(img)
      } else {
        this.showFallback(container, altText)
      }
    } else {
      // 显示网络图标
      const img = document.createElement('img')
      img.src = iconUrl
      img.alt = altText
      img.onerror = () => this.showFallback(container, altText)
      container.appendChild(img)
    }
  }

  /**
   * 显示回退图标
   */
  private showFallback(container: HTMLElement, platformName: string): void {
    const fallback = document.createElement('div')
    fallback.className = 'echosync-platform-icon-fallback'
    fallback.textContent = platformName.charAt(0).toUpperCase()
    container.appendChild(fallback)
  }

  /**
   * 创建多个平台图标组
   */
  public createIconGroup(platforms: Platform[], options: Partial<PlatformIconOptions> = {}): HTMLElement {
    const group = document.createElement('div')
    group.className = 'echosync-platform-icons-group'

    platforms.forEach(platform => {
      const icon = this.createIcon(platform, options)
      group.appendChild(icon)
    })

    return group
  }

  /**
   * 预加载图标
   */
  public async preloadIcons(platforms: Platform[]): Promise<void> {
    const promises = platforms.map(async platform => {
      try {
        const cacheKey = `${platform.id}-${platform.name}`

        // 优先使用Base64缓存数据
        if (platform.icon_base64 && platform.icon_base64.startsWith('data:image/')) {
          this.iconCache.set(cacheKey, platform.icon_base64)
        } else {
          // Fallback到原有逻辑
          const url = await this.resolveIconUrl(platform)
          if (url) {
            const isValid = await this.validateIcon(url)
            this.iconCache.set(cacheKey, isValid ? url : this.options.fallbackIcon)
          } else {
            this.iconCache.set(cacheKey, this.options.fallbackIcon)
          }
        }
      } catch (error) {
        console.error(`Failed to preload icon for ${platform.name}:`, error)
        const cacheKey = `${platform.id}-${platform.name}`
        this.iconCache.set(cacheKey, this.options.fallbackIcon)
      }
    })

    await Promise.allSettled(promises)
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.iconCache.clear()
    this.loadingSet.clear()
  }

  /**
   * 获取缓存大小
   */
  public getCacheSize(): number {
    return this.iconCache.size
  }

  /**
   * 将Blob转换为DataURL
   */
  private blobToDataUrl(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('Failed to convert blob to data URL'))
      reader.readAsDataURL(blob)
    })
  }

  /**
   * 销毁组件
   */
  public destroy(): void {
    this.clearCache()
    
    const styles = document.getElementById('echosync-platform-icon-styles')
    if (styles) {
      styles.remove()
    }
  }
}
