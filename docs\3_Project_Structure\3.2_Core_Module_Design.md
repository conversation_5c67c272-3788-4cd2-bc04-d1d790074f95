# 3.2 核心模块设计

## 插件核心架构 (重构后)

EchoSync插件经过重构，采用了高度模块化、分层清晰的架构，确保了代码的健壮性、可维护性和可扩展性。

```mermaid
graph TD
    subgraph Browser
        Popup[Popup界面]
        Options[Options界面]
        ContentScript[Content Script]
    end

    subgraph Extension Process
        subgraph Background [Background Service Worker]
            direction LR
            BackgroundIndex[index.ts]
            MessageHandler[messageHandler.ts]
            EventListeners[eventListeners.ts]
        end

        subgraph Common [Common核心服务层]
            direction TD
            Service[Service Layer]
            DAO[DAO Layer]
            Database[Database (Dexie/IndexedDB)]
        end
    end

    Popup -- 消息通信 --> MessageHandler
    Options -- 消息通信 --> MessageHandler
    ContentScript -- 消息通信 --> MessageHandler

    MessageHandler -- 调用 --> Service
    Service -- 调用 --> DAO
    DAO -- 数据操作 --> Database

    EventListeners -- 监听 --> Browser Events
    BackgroundIndex -- 初始化 --> MessageHandler & EventListeners

    style Background fill:#f0f6ff,stroke:#333,stroke-width:1px
    style Common fill:#e6ffed,stroke:#333,stroke-width:1px
```

### 1. `background` - 后台服务 (插件大脑)

后台服务是插件的核心，遵循**单一职责原则 (SRP)**，每个模块功能高度内聚。

-   **`index.ts` (主入口)**: 仅负责初始化和协调。代码行数极少，只做三件事：初始化事件监听、设置消息处理器、输出启动日志。
-   **`messageHandler.ts` (消息处理器)**: **插件的中央枢纽**。处理所有来自 `popup`, `options`, `content` 的消息请求。它不包含业务逻辑，而是将请求路由到 `common` 模块的 `Service` 层进行处理。
-   **`eventListeners.ts` (事件监听器)**: 统一管理所有 `chrome.*` API 的事件监听，如插件安装、标签页更新、快捷键等。
-   **`databaseConnection.ts`**: 管理与 `IndexedDB` 的连接。
-   **`keepAlive.ts` / `healthMonitor.ts`**: 辅助模块，分别用于Service Worker保活和健康监控。

### 2. `common` - 核心服务层 (业务逻辑核心)

`common` 模块是所有业务逻辑的所在地，采用了经典的分层架构，灵感来自Spring Boot。

-   **`Service` 层**: **业务逻辑的实现者**。
    -   被 `background/messageHandler.ts` 调用。
    -   负责处理复杂的业务逻辑，例如，组合多个DAO操作、处理数据、转换数据格式 (Entity -> DTO)。
    -   一个服务对应一个业务领域，如 `ChatHistoryService`。

-   **`DAO` (Data Access Object) 层**: **数据库的直接操作者**。
    -   被 `Service` 层调用。
    -   封装了对数据库表的 `CRUD` (增删改查) 操作。
    -   每个 `DAO` 对应一个数据库表，如 `ChatHistoryDao`。

-   **`Database` 层**: **数据库连接和定义**。
    -   使用 `Dexie.js` 封装 `IndexedDB`。
    -   定义数据库的表结构 (schema) 和版本。

-   **`types` 层**: **类型定义**。
    -   定义了数据库实体 (Entity) 和数据传输对象 (DTO)，确保类型安全。

### 3. `content` - 内容脚本 (页面交互)

内容脚本负责与AI聊天网站的页面进行交互，其内部也进行了重构。

-   **`ContentScriptManager.ts` (管理器)**: 作为内容脚本的入口和协调器，负责根据当前页面URL加载对应的适配器和模块。
-   **`adapters/` (适配器)**: 遵循**适配器模式**，为每个AI网站（如ChatGPT, Gemini）提供一个独立的适配器，封装了针对特定网站的DOM操作逻辑（如获取输入框、点击按钮）。这使得扩展新网站变得容易。
-   **`capture/` (捕捉器)**: 负责从页面DOM中捕捉聊天记录、用户输入等。
-   **`components/` (UI注入)**: 负责向页面注入React组件，如浮动按钮、提示信息等。
-   **`inject/`**: 需要直接在页面主世界(main world)执行的脚本。

### 4. `popup` & `options` - UI层

-   使用 **React** 和 **Vite** 构建，提供现代化的用户界面。
-   通过 `chrome.runtime.sendMessage` 与 `background` 通信，触发后台服务。
-   使用 **Zustand** (`stores/`) 进行轻量级的客户端状态管理。

---

## 核心设计原则总结

-   **分层架构**: `background` -> `common (Service -> DAO -> DB)` 的分层，使得业务逻辑、数据访问和底层存储完全解耦。
-   **单一职责 (SRP)**: `background` 中的每个文件，`common` 中的每个层，都有明确且单一的职责。
-   **消息驱动**: UI层与后台服务之间通过定义良好的消息进行异步通信，完全解耦。
-   **适配器模式**: `content` 脚本通过适配器模式，轻松支持多个不同的AI网站。
