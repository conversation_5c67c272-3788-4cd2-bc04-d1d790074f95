{"name": "echosync-monorepo", "version": "1.0.0", "description": "EchoSync - AI提示词同步器 Monorepo", "private": true, "workspaces": ["extension", "website"], "scripts": {"dev": "concurrently \"npm run dev:extension\" \"npm run dev:website\"", "dev:extension": "cd extension && npm run dev", "dev:website": "cd website && npm run dev", "build": "npm run build:extension && npm run build:website", "build:extension": "cd extension && npm run build", "build:website": "cd website && npm run build", "test": "npm run test:extension && npm run test:website", "test:extension": "cd extension && npm run test", "test:website": "cd website && npm run test", "lint": "npm run lint:extension && npm run lint:website", "lint:extension": "cd extension && npm run lint", "lint:website": "cd website && npm run lint", "clean": "rm -rf extension/dist extension/node_modules website/.next website/node_modules node_modules", "setup": "npm install && npm run setup:extension && npm run setup:website", "setup:extension": "cd extension && npm install", "setup:website": "cd website && npm install"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["chrome-extension", "nextjs", "ai", "prompt", "sync", "monorepo"], "author": "EchoSync Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/echosync.git"}, "bugs": {"url": "https://github.com/your-username/echosync/issues"}, "homepage": "https://echosync.ai"}