
// EchoSync 自定义事件枚举
export enum EchoSyncEventEnum {
  // 输入框获得焦点时触发
  INPUT_FOCUSED = 'echosync:input-focused',
  // 页面URL发生变化时触发
  PAGE_CHANGED = 'echosync:page-changed',
  // 元素吸附到边界时触发
  SNAP_TO_BOUNDARY = 'echosync:snap-to-boundary',
  // 点击悬浮气泡时触发
  FLOATING_BUBBLE_CLICKED = 'echosync:floating-bubble-clicked',
  // 显示已存储的提示词列表时触发
  SHOW_STORED_PROMPTS = 'echosync:show-stored-prompts',
  // 调试功能开关时触发
  DEBUG_FEATURES = 'echosync:debug-features',
  // 点击历史记录项时触发
  HISTORY_ITEM_CLICK = 'echosync:history-item-click',
  // 处理历史记录点击事件时触发
  HANDLE_HISTORY_CLICK = 'echosync:handle-history-click',
  // 历史记录气泡打开时触发
  HISTORY_BUBBLE_OPENED = 'echosync:history-bubble-opened',
  // 请求加载历史数据时触发
  REQUEST_HISTORY_DATA = 'echosync:request-history-data',
}