---
type: "development_rules"
description: "UI组件开发规则和规范"
---

# UI组件开发规则

## 组件分类和职责

### Content Script UI组件
- **位置**: `extension/src/content/components/`
- **职责**: 注入到网页中的UI元素
- **特点**: 原生DOM操作，避免框架依赖
- **使用场景**: 浮动小球、存档按钮等页面注入组件

### React UI组件
- **位置**: `extension/src/components/`
- **职责**: Popup和Options页面的UI组件
- **特点**: 基于React框架，可复用
- **使用场景**: 弹窗界面、设置页面等

### 可复用组件
- **位置**: `extension/src/components/ui/`
- **职责**: 跨模块使用的基础UI组件
- **特点**: 高度抽象，通用性强
- **使用场景**: 图标、按钮、提示等基础组件

## Content Script UI组件规范

### 1. 基础组件结构
```typescript
// 位置: extension/src/content/components/ComponentName.ts
export class ComponentName {
  private element: HTMLElement | null = null
  private isRendered: boolean = false
  private eventListeners: Array<{
    element: HTMLElement
    event: string
    handler: EventListener
  }> = []

  /**
   * 渲染组件
   */
  render(): HTMLElement | null {
    if (this.isRendered && this.element) {
      return this.element
    }

    this.element = this.createElement()
    this.applyStyles()
    this.setupEventListeners()
    this.isRendered = true
    
    return this.element
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div')
    element.className = this.getClassName()
    element.innerHTML = this.getTemplate()
    return element
  }

  /**
   * 获取CSS类名
   */
  protected getClassName(): string {
    return 'echosync-component'
  }

  /**
   * 获取HTML模板
   */
  protected getTemplate(): string {
    return `
      <div class="component-content">
        <!-- 组件内容 -->
      </div>
    `
  }

  /**
   * 应用样式
   */
  protected applyStyles(): void {
    if (!this.element) return

    // 基础样式
    Object.assign(this.element.style, {
      position: 'fixed',
      zIndex: '2147483647',
      pointerEvents: 'auto',
      userSelect: 'none',
      boxSizing: 'border-box'
    })

    // 组件特定样式
    this.applyComponentStyles()
  }

  /**
   * 应用组件特定样式
   * 子类重写此方法
   */
  protected applyComponentStyles(): void {
    // 子类实现
  }

  /**
   * 设置事件监听器
   */
  protected setupEventListeners(): void {
    // 子类实现
  }

  /**
   * 添加事件监听器（带清理记录）
   */
  protected addEventListener(
    element: HTMLElement, 
    event: string, 
    handler: EventListener
  ): void {
    element.addEventListener(event, handler)
    this.eventListeners.push({ element, event, handler })
  }

  /**
   * 获取元素引用
   */
  getElement(): HTMLElement | null {
    return this.element
  }

  /**
   * 显示组件
   */
  show(): void {
    if (this.element) {
      this.element.style.display = 'block'
    }
  }

  /**
   * 隐藏组件
   */
  hide(): void {
    if (this.element) {
      this.element.style.display = 'none'
    }
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    // 清理事件监听器
    this.eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler)
    })
    this.eventListeners = []

    // 移除DOM元素
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element)
    }

    this.element = null
    this.isRendered = false
  }
}
```

### 2. FloatingBubble 组件示例
```typescript
// 位置: extension/src/content/components/FloatingBubble.ts
export class FloatingBubble extends ComponentName {
  private position: { x: number; y: number } = { x: 0, y: 0 }
  private isHovered: boolean = false

  protected getClassName(): string {
    return 'echosync-floating-bubble'
  }

  protected getTemplate(): string {
    return `
      <div class="bubble-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      </div>
    `
  }

  protected applyComponentStyles(): void {
    if (!this.element) return

    Object.assign(this.element.style, {
      width: '50px',
      height: '50px',
      borderRadius: '50%',
      backgroundColor: '#007bff',
      color: 'white',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      boxShadow: '0 2px 10px rgba(0,0,0,0.2)',
      transition: 'all 0.3s ease',
      transform: `translate(${this.position.x}px, ${this.position.y}px)`
    })
  }

  protected setupEventListeners(): void {
    if (!this.element) return

    this.addEventListener(this.element, 'mouseenter', this.handleMouseEnter.bind(this))
    this.addEventListener(this.element, 'mouseleave', this.handleMouseLeave.bind(this))
    this.addEventListener(this.element, 'click', this.handleClick.bind(this))
  }

  private handleMouseEnter(): void {
    this.setHoverEffect(true)
  }

  private handleMouseLeave(): void {
    this.setHoverEffect(false)
  }

  private handleClick(e: MouseEvent): void {
    // 派发点击事件
    document.dispatchEvent(new CustomEvent('echosync:floating-bubble-clicked', {
      detail: { position: { x: e.clientX, y: e.clientY } }
    }))
  }

  /**
   * 设置悬停效果
   */
  setHoverEffect(isHover: boolean): void {
    if (!this.element) return

    this.isHovered = isHover
    
    if (isHover) {
      this.element.style.transform += ' scale(1.1)'
      this.element.style.backgroundColor = '#0056b3'
    } else {
      this.element.style.transform = this.element.style.transform.replace(' scale(1.1)', '')
      this.element.style.backgroundColor = '#007bff'
    }
  }

  /**
   * 设置位置
   */
  setPosition(position: { x: number; y: number }): void {
    this.position = position
    if (this.element) {
      this.element.style.transform = `translate(${position.x}px, ${position.y}px)`
    }
  }

  /**
   * 获取当前位置
   */
  getPosition(): { x: number; y: number } {
    return { ...this.position }
  }
}
```

## React UI组件规范

### 1. 基础React组件结构
```typescript
// 位置: extension/src/components/ComponentName.tsx
import React, { useState, useEffect } from 'react'
import { clsx } from 'clsx'

interface ComponentNameProps {
  className?: string
  children?: React.ReactNode
  // 其他props
}

export const ComponentName: React.FC<ComponentNameProps> = ({
  className,
  children,
  ...props
}) => {
  const [state, setState] = useState(initialState)

  useEffect(() => {
    // 副作用逻辑
  }, [])

  const handleEvent = () => {
    // 事件处理逻辑
  }

  return (
    <div 
      className={clsx('echosync-component', className)}
      {...props}
    >
      {children}
    </div>
  )
}

export default ComponentName
```

### 2. PlatformIcon 组件示例
```typescript
// 位置: extension/src/components/PlatformIcon.tsx
import React from 'react'
import { clsx } from 'clsx'

interface PlatformIconProps {
  platform: string
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export const PlatformIcon: React.FC<PlatformIconProps> = ({
  platform,
  size = 'md',
  className
}) => {
  const getIconPath = (platform: string): string => {
    const iconMap: Record<string, string> = {
      chatgpt: '/icons/chatgpt.svg',
      claude: '/icons/claude.svg',
      deepseek: '/icons/deepseek.svg',
      gemini: '/icons/gemini.svg',
      kimi: '/icons/kimi.svg'
    }
    return iconMap[platform] || '/icons/default.svg'
  }

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  }

  return (
    <img
      src={getIconPath(platform)}
      alt={`${platform} icon`}
      className={clsx(
        'echosync-platform-icon',
        sizeClasses[size],
        className
      )}
    />
  )
}

export default PlatformIcon
```

## 样式管理规范

### 1. CSS类命名规范
```css
/* 组件前缀 */
.echosync-component { }

/* 组件特定类 */
.echosync-floating-bubble { }
.echosync-history-bubble { }
.echosync-archive-button { }

/* 状态修饰符 */
.echosync-bubble--hover { }
.echosync-bubble--active { }
.echosync-bubble--dragging { }

/* 尺寸修饰符 */
.echosync-icon--sm { }
.echosync-icon--md { }
.echosync-icon--lg { }
```

### 2. 内联样式使用
```typescript
// Content Script 组件优先使用内联样式
protected applyStyles(): void {
  Object.assign(this.element.style, {
    // 位置和层级
    position: 'fixed',
    zIndex: '2147483647',
    
    // 交互
    pointerEvents: 'auto',
    userSelect: 'none',
    
    // 布局
    boxSizing: 'border-box',
    
    // 视觉
    borderRadius: '4px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
  })
}
```

### 3. CSS-in-JS 使用（React组件）
```typescript
// 使用 Tailwind CSS 类
const buttonClasses = clsx(
  'px-4 py-2 rounded-md font-medium transition-colors',
  'bg-blue-500 hover:bg-blue-600 text-white',
  'focus:outline-none focus:ring-2 focus:ring-blue-500',
  disabled && 'opacity-50 cursor-not-allowed',
  className
)
```

## 响应式设计规范

### 1. 视口适配
```typescript
// 检测视口大小并调整组件
private adjustForViewport(): void {
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  }

  if (viewport.width < 768) {
    // 移动端适配
    this.applyMobileStyles()
  } else if (viewport.width < 1024) {
    // 平板适配
    this.applyTabletStyles()
  } else {
    // 桌面端适配
    this.applyDesktopStyles()
  }
}

private applyMobileStyles(): void {
  if (!this.element) return
  
  Object.assign(this.element.style, {
    width: '40px',
    height: '40px',
    fontSize: '14px'
  })
}
```

### 2. 边界检测
```typescript
// 确保组件在可视区域内
private ensureInViewport(): void {
  if (!this.element) return

  const rect = this.element.getBoundingClientRect()
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  }

  let newX = this.position.x
  let newY = this.position.y

  // 水平边界检测
  if (rect.right > viewport.width) {
    newX = viewport.width - rect.width - 10
  }
  if (rect.left < 0) {
    newX = 10
  }

  // 垂直边界检测
  if (rect.bottom > viewport.height) {
    newY = viewport.height - rect.height - 10
  }
  if (rect.top < 0) {
    newY = 10
  }

  this.setPosition({ x: newX, y: newY })
}
```

## 可访问性规范

### 1. ARIA属性
```typescript
protected createElement(): HTMLElement {
  const element = document.createElement('div')
  
  // 设置ARIA属性
  element.setAttribute('role', 'button')
  element.setAttribute('aria-label', '浮动助手按钮')
  element.setAttribute('tabindex', '0')
  
  return element
}
```

### 2. 键盘导航
```typescript
protected setupEventListeners(): void {
  if (!this.element) return

  // 键盘事件
  this.addEventListener(this.element, 'keydown', (e: KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      this.handleClick(e as any)
    }
  })

  // 焦点事件
  this.addEventListener(this.element, 'focus', () => {
    this.element!.style.outline = '2px solid #007bff'
  })

  this.addEventListener(this.element, 'blur', () => {
    this.element!.style.outline = 'none'
  })
}
```

## 性能优化规范

### 1. 虚拟化长列表
```typescript
// 对于长列表使用虚拟化
export class VirtualizedList {
  private visibleItems: number = 10
  private itemHeight: number = 40
  private scrollTop: number = 0

  private renderVisibleItems(): void {
    const startIndex = Math.floor(this.scrollTop / this.itemHeight)
    const endIndex = Math.min(startIndex + this.visibleItems, this.totalItems)

    // 只渲染可见项
    for (let i = startIndex; i < endIndex; i++) {
      this.renderItem(i)
    }
  }
}
```

### 2. 防抖和节流
```typescript
// 防抖处理频繁事件
private debounce<T extends (...args: any[]) => void>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: number | null = null
  
  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = window.setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}

// 使用示例
private handleResize = this.debounce(() => {
  this.adjustForViewport()
}, 300)
```

### 3. 内存管理
```typescript
// 组件销毁时清理所有引用
destroy(): void {
  // 清理定时器
  if (this.animationFrame) {
    cancelAnimationFrame(this.animationFrame)
  }
  
  // 清理事件监听器
  this.eventListeners.forEach(({ element, event, handler }) => {
    element.removeEventListener(event, handler)
  })
  
  // 清理DOM引用
  this.element = null
  
  // 清理其他引用
  this.callbacks = null
  this.data = null
}
```

## 测试规范

### 1. 组件单元测试
```typescript
// 位置: extension/src/content/components/__tests__/FloatingBubble.test.ts
import { FloatingBubble } from '../FloatingBubble'

describe('FloatingBubble', () => {
  let bubble: FloatingBubble

  beforeEach(() => {
    bubble = new FloatingBubble()
  })

  afterEach(() => {
    bubble.destroy()
  })

  test('should render correctly', () => {
    const element = bubble.render()
    expect(element).toBeTruthy()
    expect(element?.className).toBe('echosync-floating-bubble')
  })

  test('should handle click events', () => {
    const element = bubble.render()
    const clickSpy = jest.fn()
    
    document.addEventListener('echosync:floating-bubble-clicked', clickSpy)
    element?.click()
    
    expect(clickSpy).toHaveBeenCalled()
  })
})
```

## 开发检查清单

### 新建UI组件时检查:
- [ ] 选择正确的组件类型（Content Script 或 React）
- [ ] 继承基础组件类或遵循组件模式
- [ ] 实现必要的生命周期方法
- [ ] 使用统一的CSS类命名规范
- [ ] 添加适当的ARIA属性
- [ ] 支持键盘导航
- [ ] 实现响应式设计
- [ ] 添加错误处理
- [ ] 编写单元测试
- [ ] 文件大小不超过 300 行
