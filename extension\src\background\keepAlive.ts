/**
 * Service Worker Keep Alive Manager
 * 管理Service Worker的保活机制，防止SW被Chrome挂起
 */

export class KeepAliveManager {
  private static readonly ALARM_NAME = 'echosync-keepalive'
  private static readonly ALARM_INTERVAL = 1 // 1分钟间隔
  private static instance: KeepAliveManager | null = null
  private isInitialized = false

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): KeepAliveManager {
    if (!KeepAliveManager.instance) {
      KeepAliveManager.instance = new KeepAliveManager()
    }
    return KeepAliveManager.instance
  }

  /**
   * 初始化保活管理器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('【EchoSync】KeepAlive manager already initialized')
      return
    }

    try {
      console.log('【EchoSync】Initializing KeepAlive manager...')

      // 设置定时保活
      await this.setupPeriodicKeepAlive()

      // 监听alarm事件
      this.setupAlarmListener()

      // 监听SW启动事件，确保保活机制始终运行
      this.setupStartupListener()

      this.isInitialized = true
      console.log('【EchoSync】KeepAlive manager initialized successfully')
    } catch (error) {
      console.error('【EchoSync】KeepAlive manager initialization failed:', error)
      throw error
    }
  }

  /**
   * 设置定时保活机制
   */
  private async setupPeriodicKeepAlive(): Promise<void> {
    try {
      // 清除现有的alarm（如果存在）
      await chrome.alarms.clear(KeepAliveManager.ALARM_NAME)

      // 创建新的定时alarm
      await chrome.alarms.create(KeepAliveManager.ALARM_NAME, {
        delayInMinutes: KeepAliveManager.ALARM_INTERVAL,
        periodInMinutes: KeepAliveManager.ALARM_INTERVAL
      })

      console.log(`【EchoSync】Periodic keep-alive alarm created (${KeepAliveManager.ALARM_INTERVAL} min interval)`)
    } catch (error) {
      console.error('【EchoSync】Failed to setup periodic keep-alive:', error)
      throw error
    }
  }

  /**
   * 设置alarm事件监听器
   */
  private setupAlarmListener(): void {
    chrome.alarms.onAlarm.addListener((alarm) => {
      if (alarm.name === KeepAliveManager.ALARM_NAME) {
        this.performKeepAlive()
      }
    })
    console.log('【EchoSync】Alarm listener setup complete')
  }

  /**
   * 设置启动监听器，确保SW重启后保活机制继续工作
   */
  private setupStartupListener(): void {
    // 监听SW启动事件
    chrome.runtime.onStartup.addListener(async () => {
      console.log('【EchoSync】Service Worker restarted, reinitializing keep-alive...')
      await this.reinitializeKeepAlive()
    })

    // 监听扩展安装/更新事件
    chrome.runtime.onInstalled.addListener(async (details) => {
      if (details.reason === 'install' || details.reason === 'update') {
        console.log('【EchoSync】Extension installed/updated, setting up keep-alive...')
        await this.reinitializeKeepAlive()
      }
    })
  }

  /**
   * 重新初始化保活机制
   */
  private async reinitializeKeepAlive(): Promise<void> {
    try {
      this.isInitialized = false
      await this.initialize()
    } catch (error) {
      console.error('【EchoSync】Failed to reinitialize keep-alive:', error)
    }
  }

  /**
   * 执行保活操作
   */
  private performKeepAlive(): void {
    const timestamp = new Date().toISOString()
    console.log(`【EchoSync】Keep-alive ping at ${timestamp}`)

    // 执行轻量级操作来保持SW活跃
    this.pingServiceWorker()

    // 可选：检查数据库连接状态
    this.checkDatabaseConnection()
  }

  /**
   * 发送ping信号保持SW活跃
   */
  private pingServiceWorker(): void {
    try {
      // 执行一个轻量级的Chrome API调用来保持SW活跃
      chrome.runtime.getPlatformInfo().then(() => {
        console.log('【EchoSync】Service Worker ping successful')
      }).catch((error) => {
        console.warn('【EchoSync】Service Worker ping failed:', error)
      })
    } catch (error) {
      console.warn('【EchoSync】Service Worker ping error:', error)
    }
  }

  /**
   * 检查数据库连接状态（可选）
   */
  private checkDatabaseConnection(): void {
    try {
      // 这里可以添加数据库连接检查逻辑
      // 例如：检查IndexedDB是否可访问
      if (typeof indexedDB !== 'undefined') {
        console.log('【EchoSync】Database connection check: IndexedDB available')
      }
    } catch (error) {
      console.warn('【EchoSync】Database connection check failed:', error)
    }
  }

  /**
   * 手动触发保活操作（用于测试或紧急情况）
   */
  async manualKeepAlive(): Promise<void> {
    console.log('【EchoSync】Manual keep-alive triggered')
    this.performKeepAlive()

    // 重置定时器
    await this.setupPeriodicKeepAlive()
  }

  /**
   * 停止保活机制
   */
  async stop(): Promise<void> {
    try {
      await chrome.alarms.clear(KeepAliveManager.ALARM_NAME)
      this.isInitialized = false
      console.log('【EchoSync】KeepAlive manager stopped')
    } catch (error) {
      console.error('【EchoSync】Failed to stop KeepAlive manager:', error)
    }
  }

  /**
   * 获取保活状态信息
   */
  async getStatus(): Promise<{
    isInitialized: boolean
    alarmExists: boolean
    nextAlarmTime?: number
  }> {
    try {
      const alarm = await chrome.alarms.get(KeepAliveManager.ALARM_NAME)
      return {
        isInitialized: this.isInitialized,
        alarmExists: !!alarm,
        nextAlarmTime: alarm?.scheduledTime
      }
    } catch (error) {
      console.error('【EchoSync】Failed to get KeepAlive status:', error)
      return {
        isInitialized: this.isInitialized,
        alarmExists: false
      }
    }
  }

  /**
   * 检查是否需要立即保活
   * 用于在关键操作前确保SW处于活跃状态
   */
  async ensureAlive(): Promise<void> {
    const status = await this.getStatus()
    
    if (!status.isInitialized) {
      console.log('【EchoSync】KeepAlive not initialized, initializing now...')
      await this.initialize()
    }

    if (!status.alarmExists) {
      console.log('【EchoSync】KeepAlive alarm missing, recreating...')
      await this.setupPeriodicKeepAlive()
    }

    // 执行一次立即保活
    this.performKeepAlive()
  }
}

// 导出单例实例
export const keepAliveManager = KeepAliveManager.getInstance()
