import React from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Home, History, Settings } from 'lucide-react'

export function Header() {
  const navigate = useNavigate()
  const location = useLocation()

  const navItems = [
    { path: '/', icon: Home, label: '首页' },
    { path: '/history', icon: History, label: '历史' },
    { path: '/settings', icon: Settings, label: '设置' }
  ]

  return (
    <header className="popup-header">
      <div className="flex items-center space-x-1">
        <img src="/icons/icon-32.png" alt="EchoSync" className="w-6 h-6" />
        <h1 className="text-lg font-semibold">EchoSync</h1>
      </div>
      
      <nav className="flex items-center space-x-1">
        {navItems.map(({ path, icon: Icon, label }) => (
          <Button
            key={path}
            variant={location.pathname === path ? "default" : "ghost"}
            size="sm"
            onClick={() => navigate(path)}
            className="h-8 w-8 p-0"
            title={label}
          >
            <Icon className="h-4 w-4" />
          </Button>
        ))}
      </nav>
    </header>
  )
}
