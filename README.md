# EchoSync - AI提示词同步器

<div align="center">
  <h1>EchoSync</h1>
  <p><strong>跨平台AI提示词同步与管理工具</strong></p>
  <p>在一个AI平台输入，自动同步到其他平台；集中存储、分类和搜索您的AI提示词。</p>
</div>

---

## 🌟 核心功能

-   🔄 **跨平台同步**: 在一个AI聊天工具（如ChatGPT）中输入提示词，浮动窗口会出现在其他AI工具（如Claude, Gemini）页面，实现一键同步。
-   📚 **历史与存档**: 自动保存您的所有提示词，并提供存档、搜索和分类功能。
-   💻 **本地优先**: 所有数据默认存储在您本地浏览器的IndexedDB中，确保隐私和安全。
-   🌐 **多平台适配**: 内置对主流AI聊天平台的支持，并可轻松扩展。

## 🚀 快速开始

### 1. 环境要求

-   **Node.js**: `v18.0.0` 或更高版本。
-   **npm**: `v9.0.0` 或更高版本。
-   **浏览器**: 最新版本的 Google Chrome。

### 2. 安装与运行

```bash
# 1. 克隆项目仓库
# git clone https://github.com/your-username/EchoAIExtention.git

# 2. 进入项目根目录
# cd EchoAIExtention

# 3. 安装所有依赖
# 这将同时安装根目录、extension和website工作区的依赖
npm install

# 4. 启动开发服务器
# 这将同时启动插件和网站的开发模式
npm run dev
```

### 3. 加载插件

1.  启动开发服务器后，`extension` 目录下的代码会被编译到 `extension/dist`。
2.  打开 Chrome，地址栏输入 `chrome://extensions`。
3.  开启 **“开发者模式”**。
4.  点击 **“加载已解压的扩展程序”**，选择 `extension/dist` 目录。

> ✨ 至此，插件已成功运行。更详细的开发流程、调试技巧和后端部署指南，请参考我们的官方文档。

## 📚 项目文档

我们维护了一套完整的项目文档，帮助您更好地了解项目的设计、开发和未来规划。

| 分类 | 文档链接 |
| :--- | :--- |
| 📝 **项目规划与需求** | [1. 项目研究与需求](docs/1_Project_Research_and_Requirements/) |
| 🛠️ **技术选型** | [2. 技术栈](docs/2_Technology_Stack/) |
| 🏗️ **架构设计** | [3. 项目结构与核心模块设计](docs/3_Project_Structure/) |
| 👨‍💻 **开发指南** | [4. 开发环境、流程与测试指南](docs/4_Development_Guide/) |
| 🗂️ **功能规格** | [具体功能设计文档 (Specs)](specs/) |

## 🔧 技术栈

-   **Chrome 插件**: `TypeScript`, `Vite`, `React`, `Tailwind CSS`, `Zustand`, `Dexie.js (IndexedDB)`
-   **官方网站**: `Next.js`, `React`, `Tailwind CSS`, `Supabase`, `Stripe`
-   **开发工具**: `ESLint`, `Prettier`, `Jest`, `React Testing Library`

---

<div align="center">
  <p><strong>让AI提示词管理更简单，让创作更高效。</strong></p>
  <p>Made with ❤️ by the EchoSync Team</p>
</div>
