/**
 * 性能优化工具
 * 提供缓存、防抖、节流等性能优化功能
 */

/**
 * 简单的LRU缓存实现
 */
export class LRUCache<K, V> {
  private cache = new Map<K, V>()
  private maxSize: number

  constructor(maxSize: number = 100) {
    this.maxSize = maxSize
  }

  get(key: K): V | undefined {
    if (this.cache.has(key)) {
      // 移到最后（最近使用）
      const value = this.cache.get(key)!
      this.cache.delete(key)
      this.cache.set(key, value)
      return value
    }
    return undefined
  }

  set(key: K, value: V): void {
    if (this.cache.has(key)) {
      this.cache.delete(key)
    } else if (this.cache.size >= this.maxSize) {
      // 删除最久未使用的项（第一个）
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    this.cache.set(key, value)
  }

  clear(): void {
    this.cache.clear()
  }

  size(): number {
    return this.cache.size
  }
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null

  return function (...args: Parameters<T>) {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func.apply(this, args), wait)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let lastTime = 0

  return function (...args: Parameters<T>) {
    const now = Date.now()
    if (now - lastTime >= wait) {
      lastTime = now
      func.apply(this, args)
    }
  }
}

/**
 * 选择器缓存
 */
export class SelectorCache {
  private cache = new LRUCache<string, Element | null>(50)
  private observedElements = new Set<Element>()

  constructor() {
    // 监听DOM变化，清理缓存
    this.setupMutationObserver()
  }

  private setupMutationObserver(): void {
    const observer = new MutationObserver(
      debounce(() => {
        this.cache.clear()
        console.log('【PerformanceOptimizer】Selector cache cleared due to DOM changes')
      }, 500)
    )

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true
    })
  }

  querySelector(selector: string): Element | null {
    const cached = this.cache.get(selector)
    if (cached !== undefined) {
      // 检查元素是否仍在DOM中
      if (cached && document.contains(cached)) {
        return cached
      }
    }

    const element = document.querySelector(selector)
    this.cache.set(selector, element)
    
    if (element) {
      this.observedElements.add(element)
    }

    return element
  }

  querySelectorAll(selector: string): NodeListOf<Element> {
    // 对于querySelectorAll，我们不缓存结果，因为NodeList可能变化
    return document.querySelectorAll(selector)
  }

  clear(): void {
    this.cache.clear()
    this.observedElements.clear()
  }

  size(): number {
    return this.cache.size()
  }
}

/**
 * 批量DOM操作优化器
 */
export class BatchDOMOperator {
  private operations: (() => void)[] = []
  private scheduled = false

  addOperation(operation: () => void): void {
    this.operations.push(operation)
    this.scheduleExecution()
  }

  private scheduleExecution(): void {
    if (this.scheduled) return

    this.scheduled = true
    requestAnimationFrame(() => {
      this.executeOperations()
      this.scheduled = false
    })
  }

  private executeOperations(): void {
    const ops = this.operations.splice(0)
    
    // 批量执行DOM操作
    for (const operation of ops) {
      try {
        operation()
      } catch (error) {
        console.error('【PerformanceOptimizer】Batch DOM operation error:', error)
      }
    }
  }

  clear(): void {
    this.operations = []
    this.scheduled = false
  }
}

/**
 * 内存使用监控器
 */
export class MemoryMonitor {
  private static instance: MemoryMonitor
  private intervalId: NodeJS.Timeout | null = null

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor()
    }
    return MemoryMonitor.instance
  }

  startMonitoring(intervalMs: number = 30000): void {
    if (this.intervalId) return

    this.intervalId = setInterval(() => {
      this.logMemoryUsage()
    }, intervalMs)

    console.log('【PerformanceOptimizer】Memory monitoring started')
  }

  stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
      console.log('【PerformanceOptimizer】Memory monitoring stopped')
    }
  }

  private logMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      console.log('【PerformanceOptimizer】Memory usage:', {
        used: `${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`,
        total: `${Math.round(memory.totalJSHeapSize / 1024 / 1024)}MB`,
        limit: `${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)}MB`
      })
    }
  }

  getMemoryInfo(): any {
    if ('memory' in performance) {
      return (performance as any).memory
    }
    return null
  }
}

/**
 * 性能优化器主类
 */
export class PerformanceOptimizer {
  private static instance: PerformanceOptimizer
  private selectorCache: SelectorCache
  private batchDOMOperator: BatchDOMOperator
  private memoryMonitor: MemoryMonitor

  private constructor() {
    this.selectorCache = new SelectorCache()
    this.batchDOMOperator = new BatchDOMOperator()
    this.memoryMonitor = MemoryMonitor.getInstance()
  }

  static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer()
    }
    return PerformanceOptimizer.instance
  }

  /**
   * 优化的querySelector
   */
  querySelector(selector: string): Element | null {
    return this.selectorCache.querySelector(selector)
  }

  /**
   * 批量DOM操作
   */
  batchDOMOperation(operation: () => void): void {
    this.batchDOMOperator.addOperation(operation)
  }

  /**
   * 开始性能监控
   */
  startMonitoring(): void {
    this.memoryMonitor.startMonitoring()
  }

  /**
   * 停止性能监控
   */
  stopMonitoring(): void {
    this.memoryMonitor.stopMonitoring()
  }

  /**
   * 清理缓存
   */
  clearCaches(): void {
    this.selectorCache.clear()
    this.batchDOMOperator.clear()
    console.log('【PerformanceOptimizer】All caches cleared')
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(): any {
    return {
      selectorCacheSize: this.selectorCache.size(),
      memoryInfo: this.memoryMonitor.getMemoryInfo(),
      timestamp: Date.now()
    }
  }
}
